{"permissions": {"allow": ["Bash(ls:*)", "Bash(rm:*)", "Bash(start index.html)", "Bash(cmd.exe /C start test-ota-channels.html)", "Bash(cmd.exe /C start test-batch-settings.html)", "<PERSON><PERSON>(python:*)", "WebFetch(domain:)", "Bash(start \"C:\\Users\\<USER>\\Downloads\\create job\\test-multi-order-detection.html\")", "Bash(cmd /c start \"\" \"C:\\Users\\<USER>\\Downloads\\create job\\test-multi-order-detection.html\")", "Bash(where chrome)", "<PERSON><PERSON>(dir:*)", "Bash(\"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\" \"file:///C:/Users/<USER>/Downloads/create%20job/debug-multi-order.html\")", "<PERSON><PERSON>(timeout:*)", "Bash(node:*)", "Bash(\"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\" \"file:///C:/Users/<USER>/Downloads/create%20job/test-simple.html\")", "Bash(\"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\" --new-window \"file:///C:/Users/<USER>/Downloads/create%20job/debug-multi-order.html\")", "<PERSON><PERSON>(powershell:*)", "Bash(start chrome \"file:///C:/Users/<USER>/Downloads/create%20job/test-gemini-fix.html\")", "Bash(explorer \"C:\\Users\\<USER>\\Downloads\\create job\\test-gemini-fix.html\")", "Bash(grep:*)", "Bash(find:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(start chrome chrome-validation-test.html)", "Bash(start chrome \"C:\\Users\\<USER>\\Downloads\\create job\\chrome-validation-test.html\")", "Bash(rg:*)", "Bash(for file in js/learning-engine/*.js)", "Bash(do)", "Bash(if [ -f \"$file\" ])", "<PERSON><PERSON>(then)", "<PERSON><PERSON>(echo:*)", "Bash(fi)", "Bash(done)", "Bash(wc:*)", "Bash(perl:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "Bash(start chrome \"test-system-validation.html\" --new-window)", "Bash(start chrome \"file:///C:/Users/<USER>/Downloads/create%20job/index.html\")", "Bash(\"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\" \"file:///C:/Users/<USER>/Downloads/create%20job/index.html\")", "Bash(start test-language-fix.html)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(if [ -f \"style.css\" ])", "Bash(then rm style.css)", "Bash(else echo \"style.css 不存在\")", "Bash(if [ -f \"css/language-dropdown.css\" ])", "Bash(then rm css/language-dropdown.css)", "Bash(else echo \"css/language-dropdown.css 不存在\")", "Bash(git add:*)", "Bash(start test-multi-select-fixes.html)"], "deny": []}}