/**
 * @OTA_MANAGER 多订单处理管理器 - 简化版 v3.0
 * 🏷️ 标签: @OTA_MULTI_ORDER_MANAGER
 * 📝 说明: 负责多订单检测、智能分割、批量处理和UI交互管理，已简化复杂配置
 * ⚠️ 警告: 已注册，请勿重复开发
 * 重点优化：移除过度复杂配置，简化算法，保持核心功能
 * <AUTHOR>
 * @version 3.0.0-simplified
 */

// 防止重复加载
if (window.MultiOrderManager) {
    console.log('重构版多订单管理器已存在，跳过重复加载');
} else {

// 获取依赖模块 - 使用统一的服务定位器

class MultiOrderManager {
    constructor() {
        // 🚀 简化配置对象，移除过度复杂的自适应系统
        this.config = {
            minInputLength: 50,           // 最小输入长度启动检测
            debounceDelay: 1200,          // 防抖延迟
            maxOrdersPerBatch: 5,         // 每批次最大订单数
            batchDelay: 800,              // 批次间延迟（ms）
            confidenceThreshold: 0.7      // AI检测置信度阈值（固定值，移除复杂自适应）
        };

        // 🚀 简化状态管理，移除复杂的自适应状态
        this.state = {
            currentSegments: [],
            selectedOrders: new Set(),
            processedOrders: new Map(),
            batchProgress: {
                total: 0,
                completed: 0,
                failed: 0,
                isRunning: false
            }
        };

        // 4. 事件监听器（🔧 修复：添加清理机制）
        this.eventListeners = new Map();
        this.debounceTimer = null;
        this.boundEventHandlers = new Map(); // 存储绑定的事件处理器引用
        
        // 5. 缓存logger实例，避免重复获取
        this.logger = getLogger();
        
        this.init();
        this.setupCleanupMechanism(); // 设置清理机制
    }

    /**
     * 初始化管理器
     */
    init() {
        this.logger?.log('多订单管理器v2.0正在初始化...', 'info');

        this.setupEventListeners();
        this.setupInputListener();
        this.initPanelEvents();
        
        this.logger?.log('多订单管理器v2.0初始化完成', 'success');
    }

    /**
     * 设置全局事件监听器（🔧 修复：移动到setupCleanupMechanism中统一管理）
     */
    setupEventListeners() {
        // ⚠️ 事件监听器现在在setupCleanupMechanism()中统一设置和管理
        this.logger?.log('⚠️ 事件监听器已移动到清理机制中统一管理', 'info');
    }

    /**
     * 处理多订单检测事件（统一入口版本）
     * @param {object} multiOrderResult - Gemini返回的完整多订单检测结果
     * @param {string} orderText - 订单文本
     */
    handleMultiOrderDetectionUnified(multiOrderResult, orderText) {
        console.group('🔍 多订单数据流追踪 - 第6步：handleMultiOrderDetectionUnified');
        this.logger?.log(`🔄 处理多订单检测事件: ${multiOrderResult?.orderCount || 0}个订单`, 'info');

        // 在console中显示详细的处理过程
        console.log('参数检查:');
        console.log('  multiOrderResult:', multiOrderResult);
        console.log('  orderText长度:', orderText?.length);
        console.log('  orderCount:', multiOrderResult?.orderCount);
        console.log('  orders数组:', multiOrderResult?.orders);
        console.log('  orders长度:', multiOrderResult?.orders?.length);
        console.log('  isMultiOrder:', multiOrderResult?.isMultiOrder);
        console.groupEnd();

        try {
            // 验证输入参数
            if (!multiOrderResult || typeof multiOrderResult !== 'object') {
                console.error('❌ 多订单检测结果格式无效');
                throw new Error('多订单检测结果格式无效');
            }

            // 🎯 核心逻辑：根据orderCount决定处理方式
            console.log('🎯 检查条件:', {
                'orderCount > 1': multiOrderResult.orderCount > 1,
                'orders存在': !!multiOrderResult.orders,
                'orders.length > 1': multiOrderResult.orders && multiOrderResult.orders.length > 1
            });
            
            if (multiOrderResult.orderCount > 1 && multiOrderResult.orders && multiOrderResult.orders.length > 1) {
                console.log('✅ 进入多订单模式处理逻辑');
                
                // 多订单模式：直接显示多订单面板
                this.logger?.log(`✅ 确认多订单模式，显示${multiOrderResult.orders.length}个订单的面板`, 'success');
                console.log(`✅ 确认多订单模式，显示${multiOrderResult.orders.length}个订单的面板`);
                
                // 保存多订单状态
                this.state.isMultiOrderMode = true;
                this.state.parsedOrders = multiOrderResult.orders;
                this.state.multiOrderResult = multiOrderResult;
                console.log('💾 多订单状态已保存');
                
                console.group('🔍 多订单数据流追踪 - 第7步：面板显示准备');
                const panel = document.getElementById('multiOrderPanel');
                console.log('面板DOM检查:', {
                    panel: panel,
                    panelExists: !!panel,
                    panelId: panel?.id,
                    panelClasses: panel?.className,
                    panelDisplay: panel?.style.display,
                    panelOffsetHeight: panel?.offsetHeight
                });
                console.groupEnd();
                
                try {
                    // 显示多订单面板
                    console.group('🔍 多订单数据流追踪 - 第8步：调用showMultiOrderPanel');
                    console.log('传入orders数据:', multiOrderResult.orders);
                    console.log('orders数量:', multiOrderResult.orders?.length);
                    this.showMultiOrderPanel(multiOrderResult.orders);
                    console.log('✅ showMultiOrderPanel调用完成');
                    console.groupEnd();
                    
                    this.logger?.log('🎉 多订单面板显示命令已执行', 'success');
                } catch (showPanelError) {
                    console.error('❌ showMultiOrderPanel执行失败:', showPanelError);
                    this.logger?.logError('showMultiOrderPanel执行失败', showPanelError);
                }
                
            } else if (multiOrderResult.orderCount === 1) {
                // 单订单模式：隐藏多订单面板（如果之前显示过）
                this.logger?.log('✅ 确认单订单模式，隐藏多订单面板', 'info');
                this.hideMultiOrderPanel();
                
            } else {
                // 无有效订单：隐藏面板并记录
                this.logger?.log('⚠️ 无有效订单，隐藏多订单面板', 'warning');
                this.hideMultiOrderPanel();
            }
            
        } catch (error) {
            this.logger?.logError('处理多订单检测事件失败（统一入口）', error);
            this.hideMultiOrderPanel();
        }
    }

    /**
     * 处理多订单检测事件（旧版本，保留向后兼容）
     * @param {object} data - 订单数据
     * @param {string} orderText - 订单文本
     */
    handleMultiOrderDetection(data, orderText) {
        this.logger?.log('🔄 处理多订单检测事件', 'info', { 
            dataType: Array.isArray(data) ? 'array' : typeof data,
            orderLength: orderText?.length || 0 
        });

        try {
            // 处理不同格式的数据
            let processedData = data;
            
            // 如果是对象，尝试转换为数组
            if (data && typeof data === 'object' && !Array.isArray(data)) {
                // 检查是否有 orders 属性
                if (data.orders && Array.isArray(data.orders)) {
                    processedData = data.orders;
                }
                // 检查是否有 segments 属性
                else if (data.segments && Array.isArray(data.segments)) {
                    processedData = data.segments;
                }
                // 检查是否有 items 属性
                else if (data.items && Array.isArray(data.items)) {
                    processedData = data.items;
                }
                // 如果对象有多个键值对，可能每个键都是一个订单
                else {
                    const keys = Object.keys(data);
                    if (keys.length > 1) {
                        processedData = keys.map(key => ({
                            id: key,
                            ...data[key]
                        }));
                    }
                }
            }

            // 统一的多订单处理逻辑
            if (Array.isArray(processedData) && processedData.length > 1) {
                this.logger?.log(`检测到${processedData.length}个订单，显示多订单面板`, 'info');
                this.showMultiOrderPanel(processedData);
            } else if (Array.isArray(processedData) && processedData.length === 1) {
                this.logger?.log('检测到单个订单，进行智能分析', 'info');
                this.analyzeInputForMultiOrder(orderText);
            } else {
                this.logger?.log('无有效数据或无法识别格式，进行智能分析', 'info');
                // 检查是否包含多个时间点或地点，使用智能分析
                this.analyzeInputForMultiOrder(orderText);
            }
        } catch (error) {
            this.logger?.logError('处理多订单检测事件失败', error);
        }
    }

    /**
     * 处理订单状态变化
     * @param {object} orderData - 订单数据
     */
    handleOrderStateChange(orderData) {
        this.logger?.log('🔄 处理订单状态变化', 'info', orderData);

        try {
            // 如果有当前订单数据，可以进行额外的处理
            if (orderData) {
                // 这里可以添加订单状态变化的处理逻辑
                // 例如：更新UI显示、保存到历史记录等
                this.logger?.log('订单状态已更新', 'success');
            }
        } catch (error) {
            this.logger?.logError('处理订单状态变化失败', error);
        }
    }

    /**
     * 设置输入事件监听器（已禁用 - 统一使用实时分析管理器）
     * ⚠️ 注意：为避免与实时分析管理器冲突，此方法已禁用
     * 所有输入事件现在统一通过实时分析管理器 → Gemini → multiOrderDetected事件处理
     */
    setupInputListener() {
        getLogger()?.log('⚠️ 多订单管理器输入监听器已禁用（使用统一事件流程）', 'info');
        // 不再绑定重复的输入事件监听器
        // 保留此方法以确保向后兼容性
    }

    /**
     * 绑定输入事件（已禁用 - 统一使用实时分析管理器）
     * ⚠️ 注意：为避免与实时分析管理器的输入监听器冲突，此方法已禁用
     * 所有输入分析现在统一通过实时分析管理器处理
     */
    bindInputEvents() {
        getLogger()?.log('⚠️ 多订单管理器输入事件绑定已禁用（避免重复监听）', 'info');
        // 不再绑定重复的输入事件监听器
        // 保留此方法以确保向后兼容性
        // 实际的输入处理现在由实时分析管理器统一管理
    }

    /**
     * 分析输入内容是否为多订单（旧版本，现在统一入口已处理）
     * ⚠️ 注意：此方法现在主要用于向后兼容和手动触发
     * 正常流程应使用实时分析管理器的统一入口处理
     * @param {string} text - 输入文本
     * @param {Object} options - 检测选项
     */
    async analyzeInputForMultiOrder(text, options = {}) {
        const { forceDetection = false, source = 'auto' } = options;

        // 基本验证（强制检测时跳过长度限制）
        if (!forceDetection && (!text || text.trim().length < this.config.minInputLength)) {
            this.hideMultiOrderPanel();
            return;
        }

        this.logger?.log(`🔍 开始分析输入内容，长度: ${text.length}字符`, 'info');
        
        try {
            // 🚀 简化：使用固定置信度阈值，移除复杂自适应算法
            const threshold = this.config.confidenceThreshold;
            this.logger?.log(`🎯 使用固定阈值: ${threshold}`, 'info');

            // 步骤2: 分析时间点和航班特征
            const timeAnalysis = this.analyzeTimePointsAndFlights(text);

            // 步骤3: 调用Gemini服务进行一体化解析
            this.logger?.log('🤖 使用一体化Gemini AI进行多订单检测和完整解析...', 'info');

            const geminiService = getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini AI服务不可用');
            }

            // 🔧 使用自适应配置
            const geminiConfig = this.getGeminiConfig();
            geminiConfig.timeAnalysis = timeAnalysis; // 传递时间分析结果
            const geminiResult = await geminiService.detectAndSplitMultiOrdersWithVerification(text, geminiConfig);
            
            // Gemini检测结果
            this.logger?.log(`🔍 Gemini检测结果: ${geminiResult.isMultiOrder ? '多订单' : '单订单'}`, 'info');
            
            if (geminiResult.isMultiOrder && geminiResult.orders && geminiResult.orders.length > 1) {
                this.logger?.log(`✅ Gemini检测到多订单: ${geminiResult.orders.length}个完整订单`, 'success');
                
                // 传递完整解析的订单对象数组
                this.logger?.log('📋 准备显示多订单面板...', 'info');
                this.showMultiOrderPanel(geminiResult.orders);
                this.logger?.log('✅ 多订单面板显示完成', 'success');
            } else {
                this.logger?.log('📋 Gemini检测为单订单，隐藏多订单面板', 'info', {
                    reason: !geminiResult.isMultiOrder ? '非多订单' : 
                           !geminiResult.orders ? '缺少orders数组' : 
                           geminiResult.orders.length <= 1 ? '订单数量不足' : '未知原因'
                });
                this.hideMultiOrderPanel();
            }
        } catch (error) {
            this.logger?.logError('Gemini一体化分析失败', error);
            // 异常情况下隐藏面板
            this.hideMultiOrderPanel();
        }
    }

    /**
     * 传统多订单检测（基于模式匹配）
     * @param {string} text - 输入文本
     * @returns {object} 检测结果
     */
    detectMultiOrderTraditional(text) {
        if (!text || typeof text !== 'string') {
            return { isMultiOrder: false, confidence: 0, reason: '文本无效' };
        }

        const cleanText = text.trim();
        let score = 0;
        const reasons = [];

        // 1. 检查明确的订单标识
        const orderMarkers = [
            /订单\s*[：:]\s*\d+/gi,
            /order\s*[：:]\s*\d+/gi,
            /第\s*\d+\s*个?订单/gi
        ];
        
        for (const pattern of orderMarkers) {
            const matches = cleanText.match(pattern);
            if (matches && matches.length > 1) {
                score += 40;
                reasons.push(`发现${matches.length}个订单标识`);
                break;
            }
        }

        // 2. 检查数字列表模式
        const numberListPattern = /^\s*\d+\s*[、.]/gm;
        const numberMatches = cleanText.match(numberListPattern);
        if (numberMatches && numberMatches.length > 1) {
            score += 30;
            reasons.push(`发现${numberMatches.length}个数字列表项`);
        }

        // 3. 检查日期时间密度
        const dateTimePatterns = [
            /\d{4}[-\/]\d{1,2}[-\/]\d{1,2}/g,
            /\d{1,2}[:：]\d{2}/g,
            /(今天|明天|后天)/g
        ];
        
        let totalDateTimeMatches = 0;
        dateTimePatterns.forEach(pattern => {
            const matches = cleanText.match(pattern) || [];
            totalDateTimeMatches += matches.length;
        });
        
        if (totalDateTimeMatches > 2) {
            score += 20;
            reasons.push(`发现${totalDateTimeMatches}个时间日期`);
        }

        // 4. 检查分隔符
        const separators = [
            /\n\s*[-=]{3,}\s*\n/g,
            /\n\s*\n\s*\n/g
        ];
        
        for (const pattern of separators) {
            const matches = cleanText.match(pattern);
            if (matches && matches.length > 0) {
                score += 15;
                reasons.push('发现明确分隔符');
                break;
            }
        }

        // 5. 文本长度和复杂度分析
        const lines = cleanText.split('\n').filter(line => line.trim().length > 10);
        if (lines.length > 8) {
            score += 10;
            reasons.push(`文本有${lines.length}行，可能包含多个订单`);
        }

        const confidence = Math.min(score / 100, 1);
        const isMultiOrder = confidence > 0.3;

        return {
            isMultiOrder,
            confidence,
            score,
            reasons: reasons.join('; ')
        };
    }

    /**
     * AI多订单检测（直接调用Gemini服务）
     * @param {string} text - 输入文本
     * @returns {Promise<object>} AI检测结果
     */
    async detectMultiOrderAI(text) {
        
        try {
            const geminiService = getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini AI服务不可用');
            }
            
            // 直接调用Gemini服务（使用统一配置）
            const result = await geminiService.detectAndSplitMultiOrdersWithVerification(text, this.getGeminiConfig());
            
            // 返回兼容的格式
            return {
                isMultiOrder: result.isMultiOrder || false,
                confidence: result.confidence || 0,
                orderCount: result.orderCount || 1,
                reason: result.analysis || '完全由Gemini AI处理'
            };
        } catch (error) {
            this.logger?.logError('AI多订单检测失败', error);
            return { 
                isMultiOrder: false, 
                confidence: 0, 
                orderCount: 1,
                reason: `AI检测失败: ${error.message}` 
            };
        }
    }
    /**
     * 智能分割订单文本（完全由Gemini处理）
     * @param {string} text - 输入文本
     * @returns {Promise<Array>} 分割后的订单对象数组
     */
    async smartSplitOrderText(text) {
        if (!text || typeof text !== 'string') {
            return [text || ''];
        }

        try {
            const geminiService = getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini AI服务不可用');
            }
            
            // 直接使用Gemini AI完全处理分割（使用统一配置）
            const result = await geminiService.detectAndSplitMultiOrdersWithVerification(text, this.getGeminiConfig());
            
            // 更新当前分段状态 - 现在存储完整的订单对象
            this.state.currentSegments = result.orders || [text];
            
            this.logger?.log(`📋 Gemini智能分割完成: ${result.orders ? result.orders.length : 1} 个完整订单`, 'success', {
                isMultiOrder: result.isMultiOrder,
                confidence: result.confidence,
                analysis: result.analysis
            });
            
            return result.orders || [text];
            
        } catch (error) {
            this.logger?.logError('Gemini智能分割失败，返回原文本', error);
            return [text];
        }
    }

    /**
     * 显示多订单面板（适配新的浮窗结构）
     * @param {Array} orders - 完整解析的订单对象数组
     */
    showMultiOrderPanel(orders) {
        console.log('🔄 showMultiOrderPanel开始执行', { orders: orders, ordersLength: orders?.length });
        this.logger?.log('🔄 开始显示多订单浮窗面板...', 'info');
        
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        console.log('🔍 multiOrderPanel元素检查:', {
            panel: multiOrderPanel,
            exists: !!multiOrderPanel,
            id: multiOrderPanel?.id,
            className: multiOrderPanel?.className
        });
        
        if (!multiOrderPanel) {
            console.error('❌ 多订单面板元素不存在');
            this.logger?.log('❌ 多订单面板元素不存在', 'error');
            return;
        }

        // 🔧 增强调试：显示前状态检查
        this.logger?.log('🔍 面板显示前状态检查:', 'info', {
            ordersCount: orders?.length || 0,
            panelExists: !!multiOrderPanel,
            currentClasses: multiOrderPanel.className,
            currentDisplay: multiOrderPanel.style.display,
            isHidden: multiOrderPanel.classList.contains('hidden')
        });

        this.logger?.log(`📋 准备显示${orders.length}个订单`, 'info');

        // 存储订单数据到状态
        this.state.parsedOrders = orders;
        
        // 更新面板内容
        this.logger?.log('🔧 更新面板内容...', 'info');
        console.log('🔧 即将调用updatePanelContent');
        try {
            this.updatePanelContent(orders);
            console.log('✅ updatePanelContent调用完成');
        } catch (updateError) {
            console.error('❌ updatePanelContent执行失败:', updateError);
            this.logger?.logError('updatePanelContent执行失败', updateError);
            throw updateError;
        }
        
        // 显示浮窗面板 - 简化版本
        this.logger?.log('👁️ 显示浮窗面板...', 'info');
        console.log('🔧 移除hidden类并显示面板...');
        
        // 确保面板可见
        multiOrderPanel.classList.remove('hidden');
        multiOrderPanel.style.display = 'flex';
        
        console.log('✅ 面板已显示', { 
            className: multiOrderPanel.className,
            display: multiOrderPanel.style.display,
            visible: multiOrderPanel.offsetHeight > 0,
            zIndex: window.getComputedStyle(multiOrderPanel).zIndex
        });

        // 重置状态
        this.state.selectedOrders.clear();
        this.state.processedOrders.clear();
        
        // 默认选中所有订单
        orders.forEach((_, index) => {
            this.state.selectedOrders.add(index);
        });
        
        this.updateSelectedCount();
        this.updateOrderStats(orders.length);

        // 确保面板可见和居中
        this.ensurePanelVisible();

        // 启用浮窗增强功能
        console.log('🔧 启用拖拽功能...');
        this.addPanelDragFeature();
        console.log('✅ 浮窗增强功能启用完成');

        console.log('🎉 showMultiOrderPanel执行完全成功', {
            ordersCount: orders.length,
            finalPanelState: {
                className: multiOrderPanel.className,
                display: multiOrderPanel.style.display,
                offsetHeight: multiOrderPanel.offsetHeight,
                offsetWidth: multiOrderPanel.offsetWidth
            }
        });
        this.logger?.log(`✅ 多订单浮窗面板已显示，包含${orders.length}个完整订单`, 'success');
    }

    /**
     * 隐藏多订单面板（适配浮窗结构）
     */
    hideMultiOrderPanel() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (multiOrderPanel) {
            // 简化隐藏逻辑
            multiOrderPanel.classList.add('hidden');
            multiOrderPanel.style.display = 'none';
            
            console.log('✅ 多订单面板已隐藏', {
                className: multiOrderPanel.className,
                display: multiOrderPanel.style.display
            });
            
            getLogger()?.log('🚪 多订单浮窗面板已关闭', 'info');
        }
    }

    /**
     * 更新面板内容（显示完整解析的订单对象）- 增强版，包含批量控制面板和Paging服务检测
     * @param {Array} orders - 完整解析的订单对象数组
     */
    updatePanelContent(orders) {
        console.log('🔧 updatePanelContent开始执行', { orders: orders, ordersLength: orders?.length });
        
        const orderList = document.querySelector('#multiOrderPanel .multi-order-list');
        console.log('🔍 orderList容器检查:', {
            orderList: orderList,
            exists: !!orderList,
            className: orderList?.className
        });
        
        if (!orderList) {
            console.error('❌ 多订单列表容器不存在');
            getLogger()?.log('多订单列表容器不存在', 'warn');
            return;
        }

        try {
            console.log('🔧 开始处理Paging服务...');
            // 应用Paging服务自动识别和处理
            const processedOrders = this.processPagingServiceForOrders(orders);
            console.log('✅ Paging服务处理完成', { processedOrdersLength: processedOrders?.length });

            // 🔍 分析数据流转过程
            this.analyzeDataFlowTransformation(orders, processedOrders);

            // 🔍 生成综合问题排查报告
            const diagnosticReport = this.generateFieldDisplayDiagnosticReport(orders, processedOrders);

            // 如果发现严重问题，在控制台显示警告
            if (diagnosticReport.summary.criticalIssues > 0) {
                console.warn(`⚠️ 发现 ${diagnosticReport.summary.criticalIssues} 个严重的字段显示问题`);
                console.warn('💡 建议查看控制台中的详细排查报告');
            }
        
            // 更新状态中的订单数据
            this.state.parsedOrders = processedOrders;
            console.log('🔧 开始生成批量控制面板...');

            // 生成批量控制面板HTML
            const batchControlHTML = this.generateBatchControlPanel();
            console.log('✅ 批量控制面板生成完成');
            console.log('🔧 开始生成订单项HTML...');
            
            // 生成订单项HTML - 显示结构化字段
            const orderItemsHTML = processedOrders.map((order, index) => {
            const orderId = `order-${index}`;
            
            // 生成订单摘要，传递正确的索引
            const summary = this.generateOrderSummary(order, index);
            
            // 检查是否为Paging订单
            const isPagingOrder = order.is_paging_order || order.carTypeId === 34;
            const pagingBadge = isPagingOrder ? '<span class="paging-badge">🏷️ 举牌</span>' : '';
            
            return `
                <div class="order-card ${isPagingOrder ? 'paging-order' : ''}" data-order-index="${index}" 
                     onclick="window.OTA.multiOrderManager.toggleOrderSelection(${index})" 
                     style="cursor: pointer;">
                    <div class="order-card-header">
                        <div class="order-selector">
                            <input type="checkbox" id="${orderId}" checked class="order-checkbox">
                            <div class="order-title">
                                <span class="order-number">订单 ${index + 1}</span>
                                ${pagingBadge}
                            </div>
                        </div>
                        <div class="order-status">
                            <span class="status-badge status-parsed">已解析</span>
                        </div>
                    </div>
                    <div class="order-card-body" onclick="window.OTA.multiOrderManager.quickEditOrder(${index}); event.stopPropagation();">
                        ${summary}
                    </div>
                </div>
            `;
            }).join('');
            console.log('✅ 订单项HTML生成完成', { orderItemsCount: processedOrders.length });

            // 将批量控制面板放入头部容器，订单列表只包含订单项
            console.log('🔧 更新batchSettingsHeader...');
            const batchSettingsHeader = document.getElementById('batchSettingsHeader');
            if (batchSettingsHeader) {
                batchSettingsHeader.innerHTML = batchControlHTML;
                console.log('✅ batchSettingsHeader更新完成');
            } else {
                console.warn('⚠️ batchSettingsHeader容器不存在');
            }
            
            console.log('🔧 更新orderList.innerHTML...');
            orderList.innerHTML = orderItemsHTML;
            console.log('✅ orderList.innerHTML更新完成');

            // 绑定选择框事件
            console.log('🔧 绑定选择框事件...');
            const checkboxes = orderList.querySelectorAll('.order-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (e) => {
                    e.stopPropagation(); // 防止触发卡片点击
                    const card = checkbox.closest('.order-card');
                    if (card) {
                        card.classList.toggle('selected', checkbox.checked);
                    }
                    this.updateSelectedCount();
                });
            });
            console.log('✅ 选择框事件绑定完成');

            // 绑定批量控制面板事件
            console.log('🔧 绑定批量控制面板事件...');
            this.bindBatchControlEvents();
            console.log('✅ 批量控制面板事件绑定完成');

            // 更新统计信息
            console.log('🔧 更新统计信息...');
            this.updateOrderStats(processedOrders.length);
            console.log('✅ 统计信息更新完成');
            
            // 显示Paging服务统计信息
            console.log('🔧 更新Paging服务统计信息...');
            this.updatePagingServiceStats(processedOrders);
            console.log('✅ Paging服务统计信息更新完成');

            // 集成学习系统UI更正功能
            console.log('🔧 集成学习系统UI...');
            this.integrateLearningSystemUI(processedOrders);
            console.log('✅ 学习系统UI集成完成');
            
            // 初始化选择计数
            this.updateSelectedCount();
            
            console.log('🎉 updatePanelContent执行完全成功');
            
        } catch (error) {
            console.error('❌ updatePanelContent执行过程中出错:', error);
            this.logger?.logError('updatePanelContent执行失败', error);
            throw error;
        }
    }

    /**
     * 处理订单的Paging服务识别和配置
     * @param {Array} orders - 原始订单数组
     * @returns {Array} 处理后的订单数组（可能包含新生成的Paging订单）
     */
    processPagingServiceForOrders(orders) {
        
        // 获取Paging服务管理器
        const pagingServiceManager = window.getPagingServiceManager ? window.getPagingServiceManager() : null;
        if (!pagingServiceManager) {
            this.logger?.log('⚠️ Paging服务管理器不可用，跳过Paging服务处理', 'warn');
            return this.processChineseLanguageDetection(orders);
        }

        try {
            // 检测所有订单的原始文本中是否包含Paging关键词
            const allOrdersText = orders.map(order => order.rawText || '').join(' ');
            const needsPagingService = pagingServiceManager.detectPagingService(allOrdersText);
            
            if (!needsPagingService) {
                this.logger?.log('📋 未检测到Paging服务关键词', 'info');
                return this.processChineseLanguageDetection(orders);
            }

            this.logger?.log('🏷️ 检测到Paging服务关键词，开始处理...', 'info');

            const processedOrders = [];
            let pagingOrdersGenerated = 0;

            orders.forEach(order => {
                // 标记原订单需要Paging服务
                const updatedOrder = {
                    ...order,
                    needsPagingService: true,
                    meetAndGreet: true
                };
                processedOrders.push(updatedOrder);

                // 为接机订单生成独立的Paging订单
                if (order.subCategoryId === 2 || order.sub_category_id === 2) {
                    try {
                        const pagingOrder = pagingServiceManager.generatePagingOrder(order);
                        
                        // 转换为前端格式
                        const frontendPagingOrder = this.convertPagingOrderToFrontendFormat(pagingOrder);
                        processedOrders.push(frontendPagingOrder);
                        pagingOrdersGenerated++;
                        
                        this.logger?.log(`✅ 为订单 "${order.customerName || 'Unknown'}" 生成Paging订单`, 'success');
                    } catch (error) {
                        this.logger?.logError(`生成Paging订单失败`, error);
                    }
                }
            });

            if (pagingOrdersGenerated > 0) {
                this.logger?.log(`🎯 Paging服务处理完成，共生成 ${pagingOrdersGenerated} 个Paging订单`, 'success');
            }

            return this.processChineseLanguageDetection(processedOrders);

        } catch (error) {
            this.logger?.logError('Paging服务处理失败', error);
            return this.processChineseLanguageDetection(orders);
        }
    }

    /**
     * 将Paging订单转换为前端格式
     * @param {Object} pagingOrder - 后端格式的Paging订单
     * @returns {Object} 前端格式的Paging订单
     */
    convertPagingOrderToFrontendFormat(pagingOrder) {
        return {
            // 基础字段
            rawText: `举牌服务 - ${pagingOrder.customer_name}`,
            customerName: pagingOrder.customer_name,
            customerContact: pagingOrder.customer_contact,
            customerEmail: pagingOrder.customer_email,
            pickup: pagingOrder.pickup,
            dropoff: pagingOrder.dropoff || '举牌服务点',
            pickupDate: pagingOrder.pickup_date,
            pickupTime: pagingOrder.pickup_time,
            
            // 服务配置 - 使用camelCase格式以匹配前端
            passengerCount: pagingOrder.passenger_count || 0,
            luggageCount: pagingOrder.luggage_count || 0,
            flightInfo: pagingOrder.flight_info,
            otaReferenceNumber: pagingOrder.ota_reference_number,
            otaPrice: pagingOrder.price || 0,
            currency: pagingOrder.currency || 'MYR',
            
            // ID字段
            carTypeId: pagingOrder.car_type_id || 34, // Ticket
            subCategoryId: pagingOrder.sub_category_id || 2, // 接机
            drivingRegionId: pagingOrder.driving_region_id || 9, // Paging
            languagesIdArray: pagingOrder.languages_id_array || [5], // Paging
            
            // 特殊需求
            extraRequirement: pagingOrder.extra_requirement,
            babyChair: false,
            tourGuide: false,
            meetAndGreet: true,
            needsPagingService: true,
            
            // 标识字段
            isPagingOrder: true,
            relatedMainOrder: pagingOrder.related_main_order,
            
            // 时间信息
            arrivalTime: pagingOrder.arrival_time,
            departureTime: pagingOrder.departure_time,
            flightType: pagingOrder.flight_type
        };
    }

    /**
     * 更新Paging服务统计信息显示
     * @param {Array} orders - 订单数组
     */
    updatePagingServiceStats(orders) {
        const pagingServiceManager = window.getPagingServiceManager ? window.getPagingServiceManager() : null;
        
        if (!pagingServiceManager) return;

        try {
            const stats = pagingServiceManager.getPagingServiceStats(orders);
            const summary = pagingServiceManager.createPagingServiceSummary(stats);
            
            // 在多订单面板中显示Paging服务统计
            const statsContainer = document.querySelector('.order-stats');
            if (statsContainer && stats.hasPagingService) {
                const pagingStats = document.createElement('span');
                pagingStats.className = 'paging-stats';
                pagingStats.innerHTML = `🏷️ ${summary}`;
                statsContainer.appendChild(pagingStats);
            }
            
            this.logger?.log(`📊 Paging服务统计: ${summary}`, 'info');
        } catch (error) {
            this.logger?.logError('更新Paging服务统计失败', error);
        }
    }

    /**
     * 处理中文语言自动检测
     * 使用统一的中文检测器确保与单订单模组逻辑同步
     * @param {Array} orders - 订单数组
     * @returns {Array} 处理后的订单数组
     */
    processChineseLanguageDetection(orders) {
        try {
            // 简化的中文检测逻辑
            const chineseRegex = /[\u4e00-\u9fff]/;
            let chineseCount = 0;
            
            const processedOrders = orders.map(order => {
                const text = `${order.customerName} ${order.pickup} ${order.dropoff} ${order.extraRequirement}`.toLowerCase();
                const hasChinese = chineseRegex.test(text);
                
                if (hasChinese) {
                    chineseCount++;
                    // 自动设置中文+英文 - 使用统一管理器的AI分析默认值
                    try {
                        const languageManager = getLanguageManager();
                        order.languagesIdArray = languageManager.getDefaultSelectionSync('ai-analysis');
                    } catch (error) {
                        order.languagesIdArray = [4, 2]; // Chinese + English fallback
                    }
                } else {
                    // 使用统一管理器的表单默认值
                    try {
                        const languageManager = getLanguageManager();
                        order.languagesIdArray = languageManager.getDefaultSelectionSync('form');
                    } catch (error) {
                        order.languagesIdArray = [2]; // English fallback
                    }
                }
                
                return order;
            });
            
            // 记录检测统计信息
            const total = orders.length;
            const englishCount = total - chineseCount;
            this.logger?.log(
                `📊 语言检测统计: 总计${total}个订单，中文${chineseCount}个(${Math.round(chineseCount/total*100)}%)，英文${englishCount}个(${Math.round(englishCount/total*100)}%)`, 
                'info'
            );
            
            return processedOrders;
            
        } catch (error) {
            this.logger?.logError('中文语言自动检测失败', error);
            return orders;
        }
    }

    /**
     * 生成批量控制面板HTML
     * @returns {string} 批量控制面板HTML
     */
    generateBatchControlPanel() {
        // 获取OTA渠道选项
        const otaChannelOptions = this.getOtaChannelOptions();
        
        // 获取语言选择组件
        const languageComponent = this.getLanguageSelectionComponent();
        
        return `
            <div class="batch-control-panel-simple">
                <!-- 简化的批量设置区域 - 只保留下拉选择框 -->
                <div class="batch-controls-row">
                    <!-- OTA渠道 -->
                    <div class="batch-control-item">
                        <select id="batchOtaChannel" class="batch-select-compact">
                            <option value="">选择渠道...</option>
                            ${otaChannelOptions}
                        </select>
                        <button type="button" class="batch-apply-compact" onclick="window.OTA.multiOrderManager.applyBatchOtaChannel()">应用</button>
                    </div>
                    
                    <!-- 语言设置 -->
                    <div class="batch-control-item">
                        ${languageComponent}
                        <button type="button" class="batch-apply-compact" onclick="window.OTA.multiOrderManager.applyBatchLanguage()">应用</button>
                    </div>
                    
                    <!-- 服务区域 -->
                    <div class="batch-control-item">
                        <select id="batchRegion" class="batch-select-compact">
                            <option value="">选择区域...</option>
                            <option value="1">吉隆坡</option>
                            <option value="2">槟城</option>
                            <option value="3">新山</option>
                            <option value="4">沙巴</option>
                            <option value="5">新加坡</option>
                            <option value="9">举牌服务</option>
                            <option value="12">马六甲</option>
                        </select>
                        <button type="button" class="batch-apply-compact" onclick="window.OTA.multiOrderManager.applyBatchRegion()">应用</button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 切换批量操作下拉菜单
     */
    toggleBatchDropdown() {
        const dropdown = document.getElementById('batchDropdownContent');
        const arrow = document.querySelector('.dropdown-arrow');
        if (dropdown) {
            const isVisible = dropdown.style.display === 'block';
            dropdown.style.display = isVisible ? 'none' : 'block';
            if (arrow) {
                arrow.textContent = isVisible ? '▼' : '▲';
            }
        }
    }

    /**
     * 全选所有订单
     */
    selectAllOrders() {
        const checkboxes = document.querySelectorAll('.order-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            const card = checkbox.closest('.order-card');
            if (card) {
                card.classList.add('selected');
            }
        });
        this.updateSelectedCount();
    }

    /**
     * 清除所有选择
     */
    clearAllSelections() {
        const checkboxes = document.querySelectorAll('.order-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            const card = checkbox.closest('.order-card');
            if (card) {
                card.classList.remove('selected');
            }
        });
        this.updateSelectedCount();
    }

    /**
     * 更新选中订单计数
     */
    updateSelectedCount() {
        const selectedCount = document.querySelectorAll('.order-checkbox:checked').length;
        const countElement = document.getElementById('selectedOrderCount');
        if (countElement) {
            countElement.textContent = selectedCount;
        }
    }

    /**
     * 切换语言下拉菜单
     */
    toggleLanguageDropdown() {
        const content = document.getElementById('languageDropdownContent');
        const arrow = document.querySelector('#batchLanguageDropdown .dropdown-arrow');
        if (content) {
            const isVisible = content.style.display === 'block';
            content.style.display = isVisible ? 'none' : 'block';
            if (arrow) {
                arrow.textContent = isVisible ? '▼' : '▲';
            }
        }
    }

    /**
     * 更新语言选择显示
     */
    updateLanguageSelection() {
        const checkboxes = document.querySelectorAll('#languageDropdownContent input[type="checkbox"]:checked');
        const selectedText = document.getElementById('languageSelectedText');
        
        if (selectedText) {
            if (checkboxes.length === 0) {
                selectedText.textContent = '选择语言...';
            } else if (checkboxes.length === 1) {
                const label = checkboxes[0].closest('label').querySelector('.language-name').textContent;
                selectedText.textContent = label;
            } else {
                selectedText.textContent = `已选择 ${checkboxes.length} 种语言`;
            }
        }
    }

    /**
     * 应用批量OTA渠道设置
     */
    applyBatchOtaChannel() {
        const select = document.getElementById('batchOtaChannel');
        const selectedValue = select?.value;
        
        if (!selectedValue) {
            const uiManager = getService('uiManager') || window.getUIManager?.();
            uiManager?.showError('请选择OTA渠道');
            return;
        }

        const selectedOrders = document.querySelectorAll('.order-checkbox:checked');
        selectedOrders.forEach(checkbox => {
            const index = parseInt(checkbox.closest('.order-card').getAttribute('data-order-index'));
            this.updateOrderField(index, 'ota_channel', selectedValue);
        });

        const uiManager = getService('uiManager') || window.getUIManager?.();
        uiManager?.showAlert(`已为 ${selectedOrders.length} 个订单设置OTA渠道: ${selectedValue}`, 'success');
    }

    /**
     * 应用批量语言设置
     */
    applyBatchLanguage() {
        const checkboxes = document.querySelectorAll('#languageDropdownContent input[type="checkbox"]:checked');
        
        if (checkboxes.length === 0) {
            const uiManager = getService('uiManager') || window.getUIManager?.();
            uiManager?.showError('请选择至少一种语言');
            return;
        }

        const languageIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
        const selectedOrders = document.querySelectorAll('.order-checkbox:checked');
        
        selectedOrders.forEach(checkbox => {
            const index = parseInt(checkbox.closest('.order-card').getAttribute('data-order-index'));
            this.updateOrderField(index, 'languages_id_array', languageIds);
        });

        const uiManager = getService('uiManager') || window.getUIManager?.();
        uiManager?.showAlert(`已为 ${selectedOrders.length} 个订单设置语言需求`, 'success');
    }

    /**
     * 应用批量区域设置
     */
    applyBatchRegion() {
        const select = document.getElementById('batchRegion');
        const selectedValue = select?.value;
        
        if (!selectedValue) {
            const uiManager = getService('uiManager') || window.getUIManager?.();
            uiManager?.showError('请选择服务区域');
            return;
        }

        const selectedOrders = document.querySelectorAll('.order-checkbox:checked');
        selectedOrders.forEach(checkbox => {
            const index = parseInt(checkbox.closest('.order-card').getAttribute('data-order-index'));
            this.updateOrderField(index, 'driving_region_id', parseInt(selectedValue));
        });

        const uiManager = getService('uiManager') || window.getUIManager?.();
        uiManager?.showAlert(`已为 ${selectedOrders.length} 个订单设置服务区域`, 'success');
    }

    /**
     * 更新订单统计信息
     * @param {number} orderCount - 订单数量
     */
    updateOrderStats(orderCount) {
        const countElement = document.getElementById('multiOrderCount');
        
        if (countElement) {
            countElement.textContent = `${orderCount} 个订单`;
        }
        
        // 日期范围元素已移除
    }

    /**
     * 更新选中订单数量显示
     */
    updateSelectedCount() {
        const checkboxes = document.querySelectorAll('.order-checkbox:checked');
        const totalCheckboxes = document.querySelectorAll('.order-checkbox');
        
        // 更新footer中的计数器（保持兼容性）
        const countElement = document.getElementById('selectedOrderCount');
        if (countElement) {
            countElement.textContent = `已选择 ${checkboxes.length} 个订单`;
        }
        
        // 头部统计信息已移除，这里不再更新
    }

    /**
     * 筛选订单
     * @param {string} filterValue - 筛选值 (all, completed, pending, warning, error)
     */
    filterOrders(filterValue) {
        const orderCards = document.querySelectorAll('.order-card');
        
        orderCards.forEach(card => {
            const statusIcon = card.querySelector('.status-icon');
            let shouldShow = true;
            
            if (filterValue !== 'all' && statusIcon) {
                const hasStatus = statusIcon.classList.contains(`status-${filterValue}`);
                shouldShow = hasStatus;
            }
            
            card.style.display = shouldShow ? 'block' : 'none';
        });
        
        this.logger?.log(`筛选订单: ${filterValue}`, 'info');
    }

    /**
     * 排序订单
     * @param {string} sortValue - 排序值 (created-desc, created-asc, amount-desc, amount-asc, name-asc)
     */
    sortOrders(sortValue) {
        const orderContainer = document.getElementById('multiOrderList');
        if (!orderContainer) return;
        
        const orderCards = Array.from(orderContainer.querySelectorAll('.order-card'));
        
        orderCards.sort((a, b) => {
            const indexA = parseInt(a.dataset.orderIndex);
            const indexB = parseInt(b.dataset.orderIndex);
            const orderA = this.state.parsedOrders[indexA];
            const orderB = this.state.parsedOrders[indexB];
            
            switch (sortValue) {
                case 'created-asc':
                    return indexA - indexB;
                case 'created-desc':
                    return indexB - indexA;
                case 'amount-desc':
                    const priceA = parseFloat(orderA?.otaPrice || 0);
                    const priceB = parseFloat(orderB?.otaPrice || 0);
                    return priceB - priceA;
                case 'amount-asc':
                    const priceA2 = parseFloat(orderA?.otaPrice || 0);
                    const priceB2 = parseFloat(orderB?.otaPrice || 0);
                    return priceA2 - priceB2;
                case 'name-asc':
                    const nameA = orderA?.customerName || '';
                    const nameB = orderB?.customerName || '';
                    return nameA.localeCompare(nameB);
                default:
                    return 0;
            }
        });
        
        // 重新排列DOM元素
        orderCards.forEach(card => orderContainer.appendChild(card));
        
        this.logger?.log(`排序订单: ${sortValue}`, 'info');
    }

    /**
     * 创建单个订单
     * @param {number} index - 订单索引
     */
    async createSingleOrder(index) {
        if (!this.state.parsedOrders[index]) {
            this.logger?.logError('订单不存在', new Error(`订单索引 ${index} 不存在`));
            return;
        }
        
        try {
            // 获取API服务
            const apiService = getService('apiService') || window.getAPIService?.();
            if (!apiService) {
                throw new Error('API服务不可用');
            }
            
            // 创建订单
            const order = this.state.parsedOrders[index];
            const result = await apiService.createOrder(order);
            
            if (result.success) {
                this.logger?.log(`订单 ${index + 1} 创建成功`, 'success');
                
                // 更新UI状态
                const card = document.querySelector(`.order-card[data-order-index="${index}"]`);
                if (card) {
                    const statusIcon = card.querySelector('.status-icon');
                    if (statusIcon) {
                        statusIcon.innerHTML = '✅已创建';
                        statusIcon.className = 'status-icon status-complete';
                    }
                }
                
                // 显示成功消息
                const uiManager = getService('uiManager') || window.getUIManager?.();
                uiManager?.showSuccess(`订单 ${index + 1} 创建成功！`);
            }
        } catch (error) {
            this.logger?.logError(`创建订单 ${index + 1} 失败`, error);
            
            // 显示错误消息
            const uiManager = getService('uiManager') || window.getUIManager?.();
            uiManager?.showError(`创建订单失败: ${error.message}`);
        }
    }

    /**
     * 编辑指定订单
     * @param {number} index - 订单索引
     */
    editOrder(index) {
        this.logger?.log(`编辑订单 ${index + 1}`, 'info');

        try {
            if (index < 0 || index >= this.state.currentSegments.length) {
                throw new Error(`订单索引 ${index} 超出范围`);
            }

            const orderInput = document.getElementById('orderInput');
            if (!orderInput) {
                throw new Error('订单输入框不存在');
            }

            // 将选中的订单片段填充到主输入框
            orderInput.value = this.state.currentSegments[index].trim();
            
            // 隐藏多订单面板
            this.hideMultiOrderPanel();
            
            // 显示返回多订单模式按钮
            const returnBtn = document.getElementById('returnToMultiOrder');
            if (returnBtn) {
                returnBtn.classList.remove('hidden');
                this.logger?.log('显示返回多订单模式按钮', 'info');
            }
            
            // 滚动到输入框并聚焦
            orderInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
            orderInput.focus();
            
            // 触发输入事件以启动实时分析（🔧 修复：添加标记防止递归）
            const event = new Event('input', { bubbles: true });
            event._programmaticTrigger = true;
            orderInput.dispatchEvent(event);

            this.logger?.log(`订单 ${index + 1} 已载入编辑器`, 'success');

        } catch (error) {
            this.logger?.logError(`编辑订单失败: ${error.message}`, error);
            const uiManager = getService('uiManager') || window.getUIManager?.();
            uiManager?.showError(`编辑订单失败: ${error.message}`);
        }
    }

    /**
     * 🔧 处理指定订单（AI解析 - 增强异步错误处理链）
     * @param {number} index - 订单索引
     */
    async processOrder(index) {
        this.logger?.log(`开始处理订单 ${index + 1}`, 'info');

        try {
            if (index < 0 || index >= this.state.currentSegments.length) {
                throw new Error(`订单索引 ${index} 超出范围`);
            }

            const orderText = this.state.currentSegments[index].trim();
            if (!orderText) {
                throw new Error(`订单 ${index + 1} 文本为空`);
            }
            
            // 更新UI状态
            this.updateOrderProcessingStatus(index, 'processing');

            // 调用Gemini AI分析（增加重试机制）
            const geminiService = getGeminiService();
            if (!geminiService || !geminiService.isAvailable()) {
                throw new Error('Gemini AI服务不可用');
            }

            let result = null;
            let retryCount = 0;
            const maxRetries = 3;
            
            while (retryCount < maxRetries) {
                try {
                    result = await geminiService.parseOrder(orderText);
                    break; // 成功则退出重试循环
                } catch (apiError) {
                    retryCount++;
                    this.logger?.logError(`订单 ${index + 1} API调用失败（尝试 ${retryCount}/${maxRetries}）`, apiError);
                    
                    if (retryCount >= maxRetries) {
                        throw new Error(`API调用失败，已重试 ${maxRetries} 次: ${apiError.message}`);
                    }
                    
                    // 指数退避延迟
                    const delay = Math.pow(2, retryCount) * 1000; // 2, 4, 8秒
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
            
            if (!result) {
                throw new Error('无法获取解析结果');
            }
            
            // 验证结果完整性
            if (!result.success) {
                throw new Error(result.message || '订单解析失败');
            }
            
            if (!result.data) {
                throw new Error('解析结果缺少数据');
            }
            
            // 验证关键字段
            const requiredFields = ['customer_name', 'pickup_location', 'pickup_date'];
            const missingFields = requiredFields.filter(field => !result.data[field]);
            
            if (missingFields.length > 0) {
                this.logger?.logError(`订单 ${index + 1} 缺少关键字段`, { missingFields });
                // 不中断处理，而是标记为警告状态
            }

            // 标准化数据格式
            const standardizedData = this.standardizeOrderData(result.data);
            
            // 存储解析结果
            this.state.processedOrders.set(index, {
                rawText: orderText,
                parsedData: standardizedData,
                confidence: result.confidence || 0,
                timestamp: Date.now(),
                retryCount: retryCount,
                status: 'success'
            });

            // 更新应用状态
            const appState = getAppState();
            if (appState) {
                try {
                    appState.setCurrentOrder({
                        rawText: orderText,
                        parsedData: standardizedData,
                        confidence: result.confidence || 0,
                        source: 'multi-order-single',
                        orderIndex: index,
                        retryCount: retryCount
                    });
                } catch (stateError) {
                    this.logger?.logError(`更新应用状态失败: ${stateError.message}`, stateError);
                }
            }

            // 更新UI
            this.updateOrderProcessingStatus(index, 'success');
            this.showOrderDetails(index, standardizedData);

            this.logger?.log(`订单 ${index + 1} 处理完成`, 'success', {
                confidence: result.confidence,
                retryCount: retryCount,
                dataKeys: Object.keys(standardizedData || {}),
                missingFields: missingFields || []
            });

        } catch (error) {
            this.logger?.logError(`处理订单 ${index + 1} 失败: ${error.message}`, error);
            
            // 增强错误处理：分类错误类型
            const errorType = this.classifyErrorType(error);
            
            // 存储失败状态
            this.state.processedOrders.set(index, {
                rawText: this.state.currentSegments[index]?.trim() || '',
                parsedData: null,
                confidence: 0,
                timestamp: Date.now(),
                status: 'error',
                error: {
                    message: error.message,
                    type: errorType,
                    stack: error.stack
                }
            });
            
            this.updateOrderProcessingStatus(index, 'error', error.message);
            
            // 只在严重错误时显示用户提示，避免过度打扰
            if (errorType !== 'validation') {
                const uiManager = getService('uiManager') || window.getUIManager?.();
                uiManager?.showError(`处理订单 ${index + 1} 失败: ${error.message}`);
            }
            
            // 记录详细错误信息
            this.logErrorDetails(error, index);
        }
    }

    /**
     * 🔧 分类错误类型
     * @param {Error} error - 错误对象
     * @returns {string} 错误类型
     */
    classifyErrorType(error) {
        const message = error.message.toLowerCase();
        
        if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {
            return 'network';
        } else if (message.includes('api') || message.includes('service')) {
            return 'api';
        } else if (message.includes('validation') || message.includes('required') || message.includes('invalid')) {
            return 'validation';
        } else if (message.includes('range') || message.includes('index')) {
            return 'range';
        } else {
            return 'unknown';
        }
    }

    /**
     * 预览订单详情
     * @param {number} index - 订单索引
     */
    previewOrder(index) {
        const processedOrder = this.state.processedOrders.get(index);
        if (!processedOrder) {
            const uiManager = getService('uiManager') || window.getUIManager?.();
            uiManager?.showError('订单未解析，请先点击解析按钮');
            return;
        }

        const detailsElement = document.querySelector(`.order-item[data-order-index="${index}"] .order-details`);
        if (detailsElement) {
            detailsElement.style.display = detailsElement.style.display === 'none' ? 'block' : 'none';
        }
    }

    /**
     * 更新订单处理状态
     * @param {number} index - 订单索引
     * @param {string} status - 状态：processing, success, error
     * @param {string} message - 额外信息
     */
    updateOrderProcessingStatus(index, status, message = '') {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const statusBadge = orderItem.querySelector('.status-badge');
        const processBtn = orderItem.querySelector('.process-order-btn');
        const previewBtn = orderItem.querySelector('.preview-order-btn');

        if (statusBadge) {
            statusBadge.className = 'status-badge';
            switch (status) {
                case 'processing':
                    statusBadge.classList.add('status-processing');
                    statusBadge.textContent = '解析中...';
                    break;
                case 'success':
                    statusBadge.classList.add('status-success');
                    statusBadge.textContent = '已解析';
                    break;
                case 'error':
                    statusBadge.classList.add('status-error');
                    statusBadge.textContent = '解析失败';
                    break;
                default:
                    statusBadge.classList.add('status-pending');
                    statusBadge.textContent = '待处理';
            }
        }

        if (processBtn) {
            switch (status) {
                case 'processing':
                    processBtn.disabled = true;
                    processBtn.textContent = '🔄 解析中...';
                    break;
                case 'success':
                    processBtn.disabled = false;
                    processBtn.textContent = '✅ 重新解析';
                    processBtn.classList.remove('btn-primary');
                    processBtn.classList.add('btn-outline');
                    break;
                case 'error':
                    processBtn.disabled = false;
                    processBtn.textContent = '🔄 重试';
                    processBtn.classList.remove('btn-primary');
                    processBtn.classList.add('btn-warning');
                    break;
                default:
                    processBtn.disabled = false;
                    processBtn.textContent = '🤖 解析';
            }
        }

        if (previewBtn && status === 'success') {
            previewBtn.style.display = 'inline-block';
        }

        if (message && status === 'error') {
            const orderContent = orderItem.querySelector('.order-content');
            if (orderContent) {
                const errorDiv = orderContent.querySelector('.error-message') || document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.innerHTML = `<small style="color: #dc3545;">错误: ${message}</small>`;
                if (!orderContent.contains(errorDiv)) {
                    orderContent.appendChild(errorDiv);
                }
            }
        }
    }

    /**
     * 显示订单详情
     * @param {number} index - 订单索引
     * @param {object} orderData - 解析后的订单数据
     */
    showOrderDetails(index, orderData) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const detailsElement = orderItem.querySelector('.order-details');
        if (!detailsElement) return;

        // 生成详情HTML
        const detailsHTML = `
            <div class="order-fields">
                <div class="field-group">
                    <div class="field">
                        <label>客户姓名:</label>
                        <span class="editable-field" data-field="customer_name">${orderData.customer_name || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>联系电话:</label>
                        <span class="editable-field" data-field="customer_contact">${orderData.customer_contact || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>上车地点:</label>
                        <span class="editable-field" data-field="pickup">${orderData.pickup || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>下车地点:</label>
                        <span class="editable-field" data-field="dropoff">${orderData.dropoff || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>日期:</label>
                        <span class="editable-field" data-field="pickup_date">${orderData.pickup_date || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>时间:</label>
                        <span class="editable-field" data-field="pickup_time">${orderData.pickup_time || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>乘客数:</label>
                        <span class="editable-field" data-field="passenger_count">${orderData.passenger_count || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>服务类型:</label>
                        <span>${this.getServiceTypeName(orderData.sub_category_id)}</span>
                    </div>
                </div>
                ${orderData.flight_info ? `
                <div class="field-group">
                    <div class="field">
                        <label>航班号:</label>
                        <span>${orderData.flight_info}</span>
                    </div>
                    <div class="field">
                        <label>航班时间:</label>
                        <span>${orderData.arrival_time || orderData.departure_time || 'N/A'}</span>
                    </div>
                </div>
                ` : ''}
            </div>
            <div class="order-actions-inline">
                <button type="button" class="btn btn-sm btn-outline" onclick="window.OTA.multiOrderManager.editOrderFields(${index})">
                    ✏️ 编辑字段
                </button>
                <button type="button" class="btn btn-sm btn-success" onclick="window.OTA.multiOrderManager.createSingleOrder(${index})">
                    🚀 创建此订单
                </button>
            </div>
        `;

        detailsElement.innerHTML = detailsHTML;
    }

    /**
     * Helper methods for compact layout design
     */
    getOrderStatusIcon(order) {
        const hasRequired = order.customerName && order.customerContact && order.pickup && order.pickupDate;
        if (hasRequired) {
            return '<span class="status-icon status-complete">✅就绪</span>';
        } else {
            return '<span class="status-icon status-error">❌缺信息</span>';
        }
    }

    formatRoute(order) {
        const pickup = order.pickup || order.pickupLocation || '未指定';
        const dropoff = order.dropoff || order.dropoffLocation || '未指定';
        // 分行显示完整地址信息
        return `
            <div class="route-display">
                <div class="pickup-address">
                    <span class="address-label">上车:</span>
                    <span class="address-text">${pickup}</span>
                </div>
                <div class="dropoff-address">
                    <span class="address-label">下车:</span>
                    <span class="address-text">${dropoff}</span>
                </div>
            </div>
        `;
    }

    getVehicleType(order) {
        const typeMap = {
            1: '舒适5座',
            2: '豪华轿车', 
            3: 'MPV7座',
            4: '豪华车'
        };
        return typeMap[order.carTypeId] || '舒适5座';
    }

    formatPhone(phone) {
        if (!phone) return '未提供';
        // 格式化电话显示：+6012***
        if (phone.startsWith('+60')) {
            return phone.substr(0, 5) + '***';
        }
        return phone.length > 6 ? phone.substr(0, 6) + '***' : phone;
    }

    /**
     * 获取服务类型名称
     * @param {number} subCategoryId - 服务类型ID
     * @returns {string} 服务类型名称
     */
    getServiceTypeName(subCategoryId) {
        const serviceTypes = {
            2: '接机服务',
            3: '送机服务',
            4: '包车服务',
            5: '举牌服务'
        };
        return serviceTypes[subCategoryId] || '未知服务';
    }

    /**
     * 获取车型名称
     * @param {number} carTypeId - 车型ID
     * @returns {string} 车型名称
     */
    getCarTypeName(carTypeId) {
        // 从API服务中获取车型映射
        const apiService = getApiService();
        if (apiService && apiService.staticData && apiService.staticData.carTypes) {
            const carType = apiService.staticData.carTypes.find(type => type.id === carTypeId);
            if (carType) {
                // 简化显示，只显示主要信息
                const name = carType.name;
                const simplified = name.split('(')[0].trim(); // 取括号前的部分
                return simplified;
            }
        }
        
        // 备用映射
        const carTypes = {
            5: '5座轿车',
            15: '7座MPV',
            20: '10座面包车',
            32: 'Alphard豪华车',
            34: '票务服务',
            37: '加长5座',
            38: '4座掀背'
        };
        return carTypes[carTypeId] || '标准车型';
    }

    /**
     * 获取区域名称
     * @param {number} drivingRegionId - 区域ID
     * @returns {string} 区域名称
     */
    getRegionName(drivingRegionId) {
        // 从API服务中获取区域映射
        const apiService = getApiService();
        if (apiService && apiService.staticData && apiService.staticData.drivingRegions) {
            const region = apiService.staticData.drivingRegions.find(r => r.id === drivingRegionId);
            if (region) {
                // 简化显示，去掉括号内容
                const name = region.name;
                const simplified = name.split('(')[0].trim();
                return simplified;
            }
        }
        
        // 备用映射
        const regions = {
            1: '吉隆坡',
            2: '槟城',
            3: '新山',
            4: '沙巴',
            5: '新加坡',
            9: '举牌服务',
            12: '马六甲'
        };
        return regions[drivingRegionId] || '未知区域';
    }

    /**
     * 获取语言名称列表
     * @param {Array} languagesIdArray - 语言ID数组
     * @returns {string} 语言名称字符串
     */
    getLanguageNames(languagesIdArray) {
        if (!Array.isArray(languagesIdArray) || languagesIdArray.length === 0) {
            return '英文';
        }
        
        // 从API服务中获取语言映射
        const apiService = getApiService();
        if (apiService && apiService.staticData && apiService.staticData.languages) {
            const languageNames = languagesIdArray.map(id => {
                const language = apiService.staticData.languages.find(l => l.id === id);
                if (language) {
                    // 简化显示，只显示主要语言名
                    const name = language.name;
                    if (name.includes('(')) {
                        return name.split('(')[0].trim();
                    }
                    return name;
                }
                return null;
            }).filter(Boolean);
            
            if (languageNames.length > 0) {
                return languageNames.join(', ');
            }
        }
        
        // 备用映射
        const languages = {
            2: '英文',
            3: '马来文',
            4: '中文',
            5: '举牌',
            8: '携程司导'
        };
        
        const languageNames = languagesIdArray.map(id => languages[id] || '未知').filter(Boolean);
        return languageNames.length > 0 ? languageNames.join(', ') : '英文';
    }

    /**
     * 获取特殊需求组合
     * @param {Object} order - 订单对象
     * @returns {string} 特殊需求字符串
     */
    getSpecialRequirements(order) {
        const requirements = [];
        
        if (order.babyChair) requirements.push('儿童座椅');
        if (order.tourGuide) requirements.push('导游服务');
        if (order.meetAndGreet || order.meet_and_greet) requirements.push('迎接服务');
        if (order.extraRequirement || order.extra_requirement) {
            const extra = order.extraRequirement || order.extra_requirement;
            if (extra.trim() !== '') {
                requirements.push(extra.substring(0, 20) + (extra.length > 20 ? '...' : ''));
            }
        }
        
        return requirements.length > 0 ? requirements.join(', ') : '无';
    }

    /**
     * 编辑订单字段（内联编辑）
     * @param {number} index - 订单索引
     */
    editOrderFields(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const editableFields = orderItem.querySelectorAll('.editable-field');
        editableFields.forEach(field => {
            const currentValue = field.textContent.trim();
            const fieldName = field.getAttribute('data-field');
            
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentValue === 'N/A' ? '' : currentValue;
            input.className = 'field-input';
            input.setAttribute('data-field', fieldName);
            
            input.addEventListener('blur', () => {
                this.saveFieldEdit(index, fieldName, input.value);
                field.textContent = input.value || 'N/A';
                field.style.display = 'inline';
                input.remove();
            });
            
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    input.blur();
                }
            });
            
            field.style.display = 'none';
            field.parentNode.insertBefore(input, field.nextSibling);
            input.focus();
        });
    }

    /**
     * 保存字段编辑
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {string} value - 新值
     */
    saveFieldEdit(index, fieldName, value) {
        const processedOrder = this.state.processedOrders.get(index);
        if (processedOrder) {
            processedOrder.parsedData[fieldName] = value;
            this.state.processedOrders.set(index, processedOrder);
            getLogger()?.log(`订单 ${index + 1} 字段 ${fieldName} 已更新`, 'info');
        }
    }

    /**
     * 切换订单选择状态
     * @param {number} index - 订单索引
     */
    toggleOrderSelection(index) {
        const checkbox = document.getElementById(`order-${index}`);
        if (checkbox) {
            checkbox.checked = !checkbox.checked;
            this.updateSelectedCount();
            
            // 更新卡片视觉状态
            const card = document.querySelector(`.order-card[data-order-index="${index}"]`);
            if (card) {
                card.classList.toggle('selected', checkbox.checked);
            }
        }
    }

    /**
     * 创建单个订单
     * @param {number} index - 订单索引
     */
    async createSingleOrder(index) {
        console.group(`🔍 多订单数据流追踪 - 第11步：createSingleOrder(${index})`);
        console.log('订单索引:', index);
        
        const processedOrder = this.state.processedOrders.get(index);
        console.log('processedOrder存在:', !!processedOrder);
        console.log('processedOrder数据:', processedOrder);
        
        if (!processedOrder) {
            console.error('❌ 订单未解析');
            const uiManager = getService('uiManager') || window.getUIManager?.();
            uiManager?.showError('订单未解析，无法创建');
            console.groupEnd();
            return;
        }

        const apiService = getApiService();
        console.log('apiService存在:', !!apiService);
        
        if (!apiService) {
            console.error('❌ API服务不可用');
            const uiManager = getService('uiManager') || window.getUIManager?.();
            uiManager?.showError('API服务不可用');
            console.groupEnd();
            return;
        }

        try {
            this.logger?.log(`开始创建订单 ${index + 1}`, 'info');
            
            // 🔧 修复：使用与单订单相同的数据标准化处理
            console.log('开始数据标准化...');
            const standardizedData = this.standardizeOrderData(processedOrder.parsedData);
            console.log('标准化后的数据:', standardizedData);
            
            // 验证订单数据
            console.log('开始数据验证...');
            const validation = apiService.validateOrderData(standardizedData);
            console.log('验证结果:', validation);
            
            if (!validation.isValid) {
                console.error('❌ 数据验证失败:', validation.errors);
                const uiManager = getService('uiManager') || window.getUIManager?.();
                uiManager?.showValidationErrors(validation.errors);
                console.groupEnd();
                return;
            }

            console.group('🔍 多订单数据流追踪 - 第12步：调用GoMyHire API');
            console.log('即将调用apiService.createOrder...');
            console.log('发送的数据:', standardizedData);
            const result = await apiService.createOrder(standardizedData);
            console.log('API返回结果:', result);
            console.groupEnd();
            
            if (result.success) {
                const orderId = result.data?.id || result.data?.order_id || result.id || 'N/A';
                console.log('✅ 订单创建成功!', { orderId: orderId });
                this.logger?.log(`订单 ${index + 1} 创建成功`, 'success', { orderId });
                
                // 🔧 修复：使用与单订单相同的成功提示逻辑
                const uiManager = window.OTA?.uiManager || window.getUiManager?.();
                if (uiManager && typeof uiManager.showSimpleSuccessToast === 'function') {
                    uiManager.showSimpleSuccessToast(orderId);
                } else {
                    // 备用提示方式
                    const fallbackUiManager = getService('uiManager') || window.getUIManager?.();
                    fallbackUiManager?.showAlert(`订单创建成功！订单ID: ${orderId}`, 'success');
                }
                
                // 更新状态为已创建
                this.updateOrderProcessingStatus(index, 'created');
                
                // 🔧 修复：使用与单订单相同的历史记录逻辑
                try {
                    const historyManager = window.OTA?.orderHistoryManager || window.orderHistoryManager;
                    if (historyManager && typeof historyManager.addOrder === 'function') {
                        historyManager.addOrder(standardizedData, orderId, result);
                        this.logger?.log('订单已记录到历史', 'info', { orderId });
                    }
                } catch (historyError) {
                    this.logger?.logError('记录订单历史失败', historyError);
                }
            } else {
                const errorMessage = result.message || result.error || '订单创建失败';
                console.error('❌ API返回失败:', errorMessage);
                this.logger?.log(`订单 ${index + 1} 创建失败`, 'error', { error: errorMessage });
                const uiManager = getService('uiManager') || window.getUIManager?.();
                uiManager?.showError(`订单创建失败: ${errorMessage}`);
            }
        } catch (error) {
            console.error('❌ 创建订单异常:', error);
            this.logger?.logError(`创建订单 ${index + 1} 异常`, error);
            const uiManager = getService('uiManager') || window.getUIManager?.();
            uiManager?.showError(`创建订单异常: ${error.message}`);
        } finally {
            console.groupEnd();
        }
    }

    /**
     * 初始化面板事件
     */
    initPanelEvents() {
        // 关闭按钮
        const closeBtn = document.getElementById('closeMultiOrderBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideMultiOrderPanel();
                getLogger()?.log('多订单面板已关闭', 'info');
            });
        }

        // 全选/取消全选
        const selectAllBtn = document.getElementById('selectAllOrdersBtn');
        const deselectAllBtn = document.getElementById('deselectAllOrdersBtn');
        
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => this.selectAllOrders(true));
        }
        
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', () => this.selectAllOrders(false));
        }

        // 验证全部
        const validateAllBtn = document.getElementById('validateAllOrdersBtn');
        if (validateAllBtn) {
            validateAllBtn.addEventListener('click', () => this.processAllOrders());
        }

        // 批量创建
        const batchCreateBtn = document.getElementById('batchCreateBtn');
        const createSelectedBtn = document.getElementById('createSelectedOrdersBtn');
        
        if (batchCreateBtn) {
            batchCreateBtn.addEventListener('click', () => this.handleBatchCreate());
        }
        
        if (createSelectedBtn) {
            createSelectedBtn.addEventListener('click', () => this.createSelectedOrders());
        }

        // 点击面板外部关闭
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (multiOrderPanel) {
            multiOrderPanel.addEventListener('click', (event) => {
                if (event.target === multiOrderPanel) {
                    this.hideMultiOrderPanel();
                }
            });
        }

        // 添加面板拖拽和最小化/最大化功能
        this.addPanelDragFeature();

        // 新增：返回主页按钮
        const backToMainBtn = document.getElementById('backToMainBtn');
        if (backToMainBtn) {
            backToMainBtn.addEventListener('click', () => {
                this.hideMultiOrderPanel();
                getLogger()?.log('返回主页', 'info');
            });
        }



        getLogger()?.log('多订单面板事件已初始化', 'info');
    }

    /**
     * 选择/取消选择所有订单
     * @param {boolean} selected - 是否选中
     */
    selectAllOrders(selected) {
        const checkboxes = document.querySelectorAll('.order-item input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selected;
        });
        this.updateSelectedCount();
        getLogger()?.log(`${selected ? '全选' : '取消全选'}订单`, 'info');
    }

    /**
     * 🔧 处理所有订单（批量解析 - 修复竞态条件和异步错误链）
     */
    async processAllOrders() {
        
        // 检查是否已在运行
        if (this.state.batchProgress.isRunning) {
            this.logger?.log('⚠️ 批量解析已在运行中，跳过重复执行', 'warn');
            return;
        }

        this.logger?.log('开始批量解析所有订单', 'info');

        const orderItems = document.querySelectorAll('.order-item');
        const total = orderItems.length;
        
        if (total === 0) {
            this.logger?.log('没有订单需要处理', 'info');
            return;
        }

        // 使用状态锁防止竞态条件
        this.state.batchProgress = {
            total,
            completed: 0,
            failed: 0,
            isRunning: true,
            startTime: Date.now(),
            processingOrder: null // 当前正在处理的订单索引
        };

        this.updateBatchProgress();

        try {
            for (let i = 0; i < total; i++) {
                // 检查是否应该停止（用户可能取消）
                if (!this.state.batchProgress.isRunning) {
                    this.logger?.log('批量解析被用户中断', 'info');
                    break;
                }

                // 设置当前处理订单
                this.state.batchProgress.processingOrder = i;
                
                try {
                    await this.processOrder(i);
                    this.state.batchProgress.completed++;
                } catch (error) {
                    this.logger?.logError(`批量解析订单 ${i + 1} 失败`, error);
                    this.state.batchProgress.failed++;
                    
                    // 添加错误处理：继续处理下一个订单而不是中断
                    this.updateOrderProcessingStatus(i, 'error', error.message);
                    
                    // 增强错误处理：记录详细错误信息
                    this.logErrorDetails(error, i);
                }
                
                this.updateBatchProgress();
                
                // 添加延迟避免API过载
                if (i < total - 1 && this.state.batchProgress.isRunning) {
                    await new Promise(resolve => setTimeout(resolve, this.config.batchDelay));
                }
            }
        } catch (unexpectedError) {
            // 捕获未预期的异常
            this.logger?.logError('批量解析出现未预期错误', unexpectedError);
            this.handleUnexpectedBatchError(unexpectedError);
        } finally {
            // 确保状态正确重置
            this.state.batchProgress.isRunning = false;
            this.state.batchProgress.processingOrder = null;
            this.updateBatchProgress();
            
            const duration = Date.now() - this.state.batchProgress.startTime;
            this.logger?.log(`批量解析完成，成功: ${this.state.batchProgress.completed}, 失败: ${this.state.batchProgress.failed}, 耗时: ${duration}ms`, 'success');
            
            // 显示批量处理结果摘要
            this.showBatchProcessingSummary();
        }
    }

    /**
     * 🔧 记录详细错误信息
     * @param {Error} error - 错误对象
     * @param {number} orderIndex - 订单索引
     */
    logErrorDetails(error, orderIndex) {
        
        const errorDetails = {
            orderIndex: orderIndex,
            errorName: error.name,
            errorMessage: error.message,
            errorStack: error.stack,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        this.logger?.logError(`订单 ${orderIndex + 1} 详细错误信息`, errorDetails);
        
        // 存储错误信息用于后续分析
        if (!this.state.errorLog) {
            this.state.errorLog = [];
        }
        this.state.errorLog.push(errorDetails);
        
        // 限制错误日志大小
        if (this.state.errorLog.length > 50) {
            this.state.errorLog = this.state.errorLog.slice(-20);
        }
    }

    /**
     * 🔧 处理未预期的批量处理错误
     * @param {Error} error - 未预期的错误
     */
    handleUnexpectedBatchError(error) {
        
        this.logger?.logError('批量处理出现未预期错误', {
            error: error.message,
            stack: error.stack,
            state: {
                total: this.state.batchProgress.total,
                completed: this.state.batchProgress.completed,
                failed: this.state.batchProgress.failed
            }
        });
        
        // 显示用户友好的错误提示
        const uiManager = getService('uiManager') || window.getUIManager?.();
        if (uiManager) {
            uiManager.showError('批量处理出现意外错误，请稍后重试');
        }
    }

    /**
     * 🔧 显示批量处理结果摘要
     */
    showBatchProcessingSummary() {
        const { total, completed, failed } = this.state.batchProgress;
        const uiManager = getService('uiManager') || window.getUIManager?.();
        
        if (!uiManager) return;
        
        let message = `批量处理完成！\n总订单: ${total}\n成功: ${completed}\n失败: ${failed}`;
        
        if (failed === 0) {
            uiManager.showAlert(message, 'success');
        } else {
            uiManager.showAlert(message, failed === total ? 'error' : 'warning');
        }
    }

    /**
     * 更新批量进度显示
     */
    updateBatchProgress() {
        const statusElement = document.querySelector('.batch-create-status');
        if (!statusElement) return;

        const { total, completed, failed, isRunning } = this.state.batchProgress;
        
        if (isRunning) {
            statusElement.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${(completed + failed) / total * 100}%"></div>
                </div>
                <div class="progress-text">
                    解析进度: ${completed + failed}/${total} (成功: ${completed}, 失败: ${failed})
                </div>
            `;
            statusElement.style.display = 'block';
        } else {
            statusElement.style.display = 'none';
        }
    }

    /**
     * 批量创建处理
     */
    async handleBatchCreate() {
        // 先解析所有订单
        await this.processAllOrders();
        
        // 然后创建所有已解析的订单
        await this.createSelectedOrders();
    }

    /**
     * 创建选中的订单
     */
    async createSelectedOrders() {
        console.group('🔍 多订单数据流追踪 - 第9步：批量创建订单');
        const checkboxes = document.querySelectorAll('.order-checkbox:checked');
        console.log('选中的订单数量:', checkboxes.length);
        
        if (checkboxes.length === 0) {
            console.warn('❌ 没有选中的订单');
            const uiManager = getService('uiManager') || window.getUIManager?.();
            uiManager?.showError('没有选中的订单');
            console.groupEnd();
            return;
        }

        this.logger?.log(`开始批量创建 ${checkboxes.length} 个选中订单`, 'info');

        let successCount = 0;
        let failedCount = 0;

        for (const checkbox of checkboxes) {
            const orderItem = checkbox.closest('.order-card');
            const index = parseInt(orderItem.getAttribute('data-order-index'));
            
            console.group(`🔍 多订单数据流追踪 - 第10步：创建订单${index + 1}`);
            console.log('订单索引:', index);
            
            try {
                // 确保订单已解析
                if (!this.state.processedOrders.has(index)) {
                    console.log('订单未解析，先进行解析...');
                    await this.processOrder(index);
                }
                
                console.log('开始创建单个订单...');
                await this.createSingleOrder(index);
                successCount++;
                console.log('✅ 订单创建成功');
            } catch (error) {
                console.error('❌ 订单创建失败:', error);
                this.logger?.logError(`创建订单 ${index + 1} 失败`, error);
                failedCount++;
            }
            console.groupEnd();
            
            // 添加延迟避免API过载
            await new Promise(resolve => setTimeout(resolve, this.config.batchDelay));
        }
        console.groupEnd();

        const message = `批量创建完成！成功: ${successCount}, 失败: ${failedCount}`;
        const uiManager = getService('uiManager') || window.getUIManager?.();
        if (failedCount === 0) {
            uiManager?.showAlert(message, 'success');
        } else {
            uiManager?.showAlert(message, 'warning');
        }

        this.logger?.log(message, 'info');
    }

    /**
     * Chrome MCP 集成接口 - 重构版增强
     * 用于与Chrome Model Context Protocol集成测试
     */
    initChromeMCPIntegration() {
        this.logger?.log('🌐 初始化Chrome MCP集成接口...', 'info');

        // Chrome MCP 接口对象
        this.chromeMCP = {
            // 内容提取接口
            extractContent: async (url) => {
                try {
                    this.logger?.log(`🔍 Chrome MCP: 尝试提取 ${url} 的内容`, 'info');
                    
                    // 检查是否在Chrome MCP环境中
                    if (typeof window.chrome !== 'undefined' && window.chrome.runtime) {
                        // 实际的Chrome MCP调用将在这里实现
                        this.logger?.log('⚠️ Chrome MCP: 真实环境调用需要MCP环境支持', 'warning');
                        return { success: false, message: 'Chrome MCP环境未就绪' };
                    } else {
                        // 模拟模式用于测试
                        this.logger?.log('🧪 Chrome MCP: 模拟模式运行', 'info');
                        return {
                            success: true,
                            content: '模拟提取的页面内容：订单信息...',
                            url: url,
                            timestamp: Date.now()
                        };
                    }
                } catch (error) {
                    this.logger?.logError('Chrome MCP内容提取失败', error);
                    return { success: false, error: error.message };
                }
            },

            // 截图接口
            captureScreenshot: async (options = {}) => {
                try {
                    this.logger?.log('📷 Chrome MCP: 尝试截图', 'info');
                    
                    if (typeof window.chrome !== 'undefined' && window.chrome.runtime) {
                        this.logger?.log('⚠️ Chrome MCP: 真实截图需要MCP环境支持', 'warning');
                        return { success: false, message: 'Chrome MCP环境未就绪' };
                    } else {
                        this.logger?.log('🧪 Chrome MCP: 模拟截图模式', 'info');
                        return {
                            success: true,
                            screenshot: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
                            options: options,
                            timestamp: Date.now()
                        };
                    }
                } catch (error) {
                    this.logger?.logError('Chrome MCP截图失败', error);
                    return { success: false, error: error.message };
                }
            },

            // 自动化交互接口
            automateInteraction: async (actions) => {
                try {
                    this.logger?.log(`🤖 Chrome MCP: 执行 ${actions.length} 个自动化操作`, 'info');
                    
                    if (typeof window.chrome !== 'undefined' && window.chrome.runtime) {
                        this.logger?.log('⚠️ Chrome MCP: 真实自动化需要MCP环境支持', 'warning');
                        return { success: false, message: 'Chrome MCP环境未就绪' };
                    } else {
                        this.logger?.log('🧪 Chrome MCP: 模拟自动化操作', 'info');
                        const results = actions.map((action, index) => ({
                            actionIndex: index,
                            type: action.type || 'unknown',
                            status: 'simulated',
                            success: true
                        }));
                        
                        return {
                            success: true,
                            results: results,
                            timestamp: Date.now()
                        };
                    }
                } catch (error) {
                    this.logger?.logError('Chrome MCP自动化失败', error);
                    return { success: false, error: error.message };
                }
            },

            // 检查MCP环境状态
            checkMCPStatus: () => {
                const hasChrome = typeof window.chrome !== 'undefined';
                const hasRuntime = hasChrome && !!window.chrome.runtime;
                const hasMCP = hasRuntime && !!window.chrome.runtime.sendMessage;
                
                const status = {
                    chromeAPI: hasChrome,
                    runtime: hasRuntime,
                    mcpReady: hasMCP,
                    environment: hasMCP ? 'production' : 'simulation'
                };
                
                this.logger?.log(`🔍 Chrome MCP状态检查:`, 'info', status);
                return status;
            }
        };

        this.logger?.log('✅ Chrome MCP集成接口初始化完成', 'success');
        return this.chromeMCP;
    }

    /**
     * 测试Chrome MCP集成功能
     */
    async testChromeMCPIntegration() {
        this.logger?.log('🧪 开始测试Chrome MCP集成功能', 'info');

        try {
            // 确保MCP接口已初始化
            if (!this.chromeMCP) {
                this.initChromeMCPIntegration();
            }

            // 测试状态检查
            const status = this.chromeMCP.checkMCPStatus();
            this.logger?.log(`📊 MCP环境状态: ${status.environment}`, 'info');

            // 测试内容提取
            const contentResult = await this.chromeMCP.extractContent('https://example.com');
            this.logger?.log(`🔍 内容提取测试: ${contentResult.success ? '成功' : '失败'}`, 
                contentResult.success ? 'success' : 'warning');

            // 测试截图功能
            const screenshotResult = await this.chromeMCP.captureScreenshot({ 
                width: 800, 
                height: 600 
            });
            this.logger?.log(`📷 截图测试: ${screenshotResult.success ? '成功' : '失败'}`, 
                screenshotResult.success ? 'success' : 'warning');

            // 测试自动化交互
            const automationResult = await this.chromeMCP.automateInteraction([
                { type: 'click', selector: '.btn-test' },
                { type: 'input', selector: '#test-input', value: 'test' }
            ]);
            this.logger?.log(`🤖 自动化测试: ${automationResult.success ? '成功' : '失败'}`, 
                automationResult.success ? 'success' : 'warning');

            const testResult = {
                status: status,
                contentExtraction: contentResult.success,
                screenshot: screenshotResult.success,
                automation: automationResult.success,
                overallSuccess: true
            };

            this.logger?.log('🎉 Chrome MCP集成测试完成', 'success', testResult);
            return testResult;

        } catch (error) {
            this.logger?.logError('Chrome MCP集成测试失败', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 生成订单摘要信息（重构版v2.0 - 核心字段显示，支持Paging订单）
     * @param {Object} order - 订单对象
     * @param {number} index - 订单索引
     * @returns {string} 摘要HTML
     */
    generateOrderSummary(order, index = 0) {
        // 🔍 数据流转调试：记录订单字段映射状态
        this.debugOrderFieldMapping(order, index);

        const isSelected = this.state.selectedOrders.has(index);
        const currency = order.currency || 'MYR';
        const price = order.otaPrice ? parseFloat(order.otaPrice).toFixed(2) : '0.00';
        
        return `
            <div class="compact-card order-card" data-order-index="${index}">
                <div class="order-header compact-inline-layout">
                    <div class="inline-item">
                        <input type="checkbox" id="order-${index}" class="order-checkbox" ${isSelected ? 'checked' : ''}>
                        <label for="order-${index}" class="inline-label">📋#${(index + 1).toString().padStart(3, '0')}</label>
                    </div>
                    <div class="inline-item">${this.getOrderStatusIcon(order)}</div>
                </div>
                
                <div class="order-summary order-grid-layout">
                    <div class="order-grid-left">
                        <div class="grid-item editable-field" data-field="customerName" onclick="window.OTA.multiOrderManager.editField(${index}, 'customerName', event)">
                            <span class="grid-label">👤</span>
                            <span class="grid-value">${order.customerName || '未提供'}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item grid-item-route">
                            <span class="grid-label">📍</span>
                            <div class="grid-value">${this.formatRoute(order)}</div>
                        </div>
                        <div class="grid-item editable-field" data-field="pickupDate" onclick="window.OTA.multiOrderManager.editField(${index}, 'pickupDate', event)">
                            <span class="grid-label">📅</span>
                            <span class="grid-value">${order.pickupDate || '未指定'}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="pickupTime" onclick="window.OTA.multiOrderManager.editField(${index}, 'pickupTime', event)">
                            <span class="grid-label">⏰</span>
                            <span class="grid-value">${order.pickupTime || '未指定'}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="customerContact" onclick="window.OTA.multiOrderManager.editField(${index}, 'customerContact', event)">
                            <span class="grid-label">📞</span>
                            <span class="grid-value">${this.formatPhone(order.customerContact)}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                    </div>
                    <div class="order-grid-right">
                        <div class="grid-item editable-field" data-field="passengerCount" onclick="window.OTA.multiOrderManager.editField(${index}, 'passengerCount', event)">
                            <span class="grid-label">👥</span>
                            <span class="grid-value">${order.passengerCount || 1}人</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="luggageCount" onclick="window.OTA.multiOrderManager.editField(${index}, 'luggageCount', event)">
                            <span class="grid-label">🧳</span>
                            <span class="grid-value">${order.luggageCount || 0}件</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="price" onclick="window.OTA.multiOrderManager.editField(${index}, 'price', event)">
                            <span class="grid-label">💰</span>
                            <span class="grid-value">${currency}${price}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="vehicleType" onclick="window.OTA.multiOrderManager.editField(${index}, 'vehicleType', event)">
                            <span class="grid-label">🚗</span>
                            <span class="grid-value">${this.getCarTypeName(order.carTypeId)}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                    </div>
                </div>
                
                <div class="order-actions compact-inline-layout">
                    <button type="button" class="btn-create btn-compact" onclick="window.getMultiOrderManager().createSingleOrder(${index})">
                        创建
                    </button>
                </div>
            </div>
        `;
    }
    /**
     * 切换订单详情显示 - 重构为直接调用快捷编辑
     * @param {number} index - 订单索引
     */
    toggleOrderDetails(index) {
        // 简化：直接调用快捷编辑，而不是切换复杂的详细字段界面
        this.quickEditOrder(index);
        
        getLogger()?.log(`订单 ${index + 1} 进入快捷编辑模式`, 'info');
    }

    /**
     * 更新订单字段值 - 增强版，确保数据同步
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {*} value - 字段值
     */
    updateOrderField(index, fieldName, value) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        // 更新状态中的订单数据
        this.state.parsedOrders[index][fieldName] = value;
        
        // 执行字段验证（现在会正确找到DOM元素）
        this.validateField(index, fieldName, value);
        
        // 更新摘要显示（现在会传递正确的索引）
        this.updateOrderSummaryDisplay(index);
        
        // 如果在快捷编辑模式，确保UI同步
        const quickEditInput = document.querySelector(`.quick-edit-panel input[name="${fieldName}"]`);
        if (quickEditInput && quickEditInput.value !== value) {
            quickEditInput.value = value;
        }
        
        getLogger()?.log(`订单 ${index + 1} 的 ${fieldName} 已更新为: ${value}`, 'info');
    }

    /**
     * 验证字段值 - 修复版，支持快捷编辑模式
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {*} value - 字段值
     */
    validateField(index, fieldName, value) {
        // 根据当前编辑模式选择正确的DOM选择器
        let fieldGroup;
        
        // 检查是否在快捷编辑模式
        const quickEditPanel = document.querySelector('.quick-edit-overlay .quick-edit-panel');
        if (quickEditPanel) {
            // 快捷编辑模式下的选择器
            const input = quickEditPanel.querySelector(`input[name="${fieldName}"]`);
            fieldGroup = input?.closest('.order-field-group');
        } else {
            // 原有的选择器（用于其他编辑模式）
            fieldGroup = document.querySelector(`.order-item[data-order-index="${index}"] .order-field-group[data-field="${fieldName}"]`);
        }
        
        if (!fieldGroup) {
            getLogger()?.log(`找不到字段组: ${fieldName}`, 'warn');
            return;
        }

        const validationContainer = fieldGroup.querySelector('.field-validation-container');
        if (!validationContainer) return;

        // 必填字段列表
        const requiredFields = ['customerName', 'pickup', 'dropoff', 'pickupDate', 'otaReferenceNumber'];
        const isRequired = requiredFields.includes(fieldName);
        const isEmpty = !value || value.toString().trim() === '';

        // 清除之前的验证状态
        fieldGroup.classList.remove('invalid', 'valid');
        validationContainer.innerHTML = '';

        if (isRequired && isEmpty) {
            // 必填字段为空
            fieldGroup.classList.add('invalid');
            validationContainer.innerHTML = `<div class="field-validation-message">此字段为必填项</div>`;
        } else if (!isEmpty) {
            // 字段有值，进行特定验证
            let isValid = true;
            let message = '';

            switch (fieldName) {
                case 'customerEmail':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    isValid = emailRegex.test(value);
                    message = isValid ? '邮箱格式正确' : '邮箱格式不正确';
                    break;
                case 'customerContact':
                    const phoneRegex = /^[\+]?[0-9\-\s\(\)]{8,}$/;
                    isValid = phoneRegex.test(value);
                    message = isValid ? '电话格式正确' : '电话格式不正确';
                    break;
                case 'pickupDate':
                    const dateObj = new Date(value);
                    isValid = !isNaN(dateObj.getTime()) && dateObj >= new Date().setHours(0,0,0,0);
                    message = isValid ? '日期有效' : '日期无效或已过期';
                    break;
                case 'passengerCount':
                    const count = parseInt(value);
                    isValid = !isNaN(count) && count > 0 && count <= 50;
                    message = isValid ? '乘客数量有效' : '乘客数量必须在1-50之间';
                    break;
                case 'otaPrice':
                    const price = parseFloat(value);
                    isValid = !isNaN(price) && price > 0;
                    const i18n = this.getI18nManagerWithRetry();
                    message = isValid ? 
                        (i18n ? i18n.t('messages.priceValid') : '价格有效') : 
                        (i18n ? i18n.t('messages.priceInvalid') : '价格必须大于0');
                    break;
                default:
                    if (isRequired) {
                        message = '字段已填写';
                    }
                    break;
            }

            if (isValid && message) {
                fieldGroup.classList.add('valid');
                validationContainer.innerHTML = `<div class="field-success-message">${message}</div>`;
            } else if (!isValid) {
                fieldGroup.classList.add('invalid');
                validationContainer.innerHTML = `<div class="field-validation-message">${message}</div>`;
            }
        }
    }

    /**
     * 验证和增强OTA参考号
     * @param {number} index - 订单索引
     */
    validateAndEnhanceOtaReference(index) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        const order = this.state.parsedOrders[index];

        try {
            // 如果已有参考号，先验证其有效性
            if (order.otaReferenceNumber) {
                const geminiService = getGeminiService();
                if (geminiService && geminiService.isValidOtaReference(order.otaReferenceNumber)) {
                    this.logger?.log(`现有OTA参考号有效: ${order.otaReferenceNumber}`, 'success');
                    return order.otaReferenceNumber;
                } else {
                    this.logger?.log(`现有OTA参考号无效，将重新生成: ${order.otaReferenceNumber}`, 'warn');
                }
            }

            // 尝试从原始文本中重新提取
            if (order.rawText) {
                const geminiService = getGeminiService();
                if (geminiService && geminiService.enhancedOtaReferenceExtractor) {
                    const extractedRef = geminiService.enhancedOtaReferenceExtractor(order.rawText);
                    if (extractedRef) {
                        this.updateOrderField(index, 'otaReferenceNumber', extractedRef);
                        this.logger?.log(`从原始文本重新提取参考号: ${extractedRef}`, 'success');
                        return extractedRef;
                    }
                }
            }

            // 如果提取失败，生成新的参考号
            return this.generateOtaReference(index);

        } catch (error) {
            this.logger?.logError('验证和增强OTA参考号失败', error);
            return this.generateOtaReference(index);
        }
    }

    /**
     * 生成OTA参考号
     * @param {number} index - 订单索引
     */
    generateOtaReference(index) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        const order = this.state.parsedOrders[index];

        try {
            // 构建参考号：客户名字前缀 + 日期 + 随机数
            let prefix = '';
            if (order.customerName) {
                // 提取客户名字的前几个字符
                const cleanName = order.customerName.replace(/[^a-zA-Z0-9\u4e00-\u9fff]/g, '');
                prefix = cleanName.substring(0, 3).toUpperCase();
            } else {
                prefix = 'OTA';
            }

            // 日期部分
            let datePart = '';
            if (order.pickupDate) {
                const date = new Date(order.pickupDate);
                if (!isNaN(date.getTime())) {
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    datePart = month + day;
                }
            }
            if (!datePart) {
                const today = new Date();
                const month = String(today.getMonth() + 1).padStart(2, '0');
                const day = String(today.getDate()).padStart(2, '0');
                datePart = month + day;
            }

            // 航班号部分（如果有）
            let flightPart = '';
            if (order.flightInfo) {
                const flightMatch = order.flightInfo.match(/[A-Z]{2}\d{3,4}/);
                if (flightMatch) {
                    flightPart = flightMatch[0];
                }
            }

            // 随机数部分
            const randomPart = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

            // 组合参考号
            const otaReference = `${prefix}${datePart}${flightPart}${randomPart}`;

            // 更新字段
            this.updateOrderField(index, 'otaReferenceNumber', otaReference);

            // 更新UI中的输入框
            const input = document.querySelector(`.order-item[data-order-index="${index}"] input[name="otaReferenceNumber"]`);
            if (input) {
                input.value = otaReference;
                input.classList.remove('ota-reference-missing');
            }

            this.logger?.log(`为订单 ${index + 1} 生成OTA参考号: ${otaReference}`, 'success');
            return otaReference;

        } catch (error) {
            this.logger?.logError(`生成OTA参考号失败`, error);
            // 生成简单的随机参考号作为备用
            const simpleRef = 'OTA' + Date.now().toString().slice(-6);
            this.updateOrderField(index, 'otaReferenceNumber', simpleRef);
            return simpleRef;
        }
    }

    /**
     * 快捷编辑订单
     * @param {number} index - 订单索引
     */
    quickEditOrder(index) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        // 检查是否已经在编辑模式
        if (orderItem.classList.contains('editing')) {
            this.exitQuickEdit(index);
            return;
        }

        // 进入编辑模式
        orderItem.classList.add('editing');
        
        // 创建快捷编辑面板
        this.createQuickEditPanel(index);

        getLogger()?.log(`启动订单 ${index + 1} 快捷编辑模式`, 'info');
    }

    /**
     * 编辑单个字段
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {Event} event - 点击事件
     */
    editField(index, fieldName, event) {
        event.stopPropagation(); // 阻止事件冒泡到父元素

        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        const order = this.state.parsedOrders[index];
        const fieldElement = event.currentTarget;
        const valueElement = fieldElement.querySelector('.grid-value');

        if (!valueElement) return;

        // 如果已经在编辑状态，则退出编辑
        if (fieldElement.classList.contains('editing-field')) {
            this.exitFieldEdit(fieldElement, index, fieldName);
            return;
        }

        // 进入编辑状态
        fieldElement.classList.add('editing-field');

        // 获取当前值
        const currentValue = this.getFieldValue(order, fieldName);

        // 创建编辑输入框
        const inputElement = this.createFieldInput(fieldName, currentValue);

        // 替换显示元素
        valueElement.style.display = 'none';
        fieldElement.appendChild(inputElement);

        // 聚焦并选中文本
        inputElement.focus();
        if (inputElement.select) {
            inputElement.select();
        }

        // 绑定事件
        inputElement.addEventListener('blur', () => {
            this.saveFieldEdit(fieldElement, inputElement, index, fieldName);
        });

        inputElement.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.saveFieldEdit(fieldElement, inputElement, index, fieldName);
            } else if (e.key === 'Escape') {
                this.exitFieldEdit(fieldElement, index, fieldName);
            }
        });

        getLogger()?.log(`开始编辑字段: ${fieldName}`, 'info');
    }

    /**
     * 获取字段值
     * @param {Object} order - 订单对象
     * @param {string} fieldName - 字段名
     * @returns {string} 字段值
     */
    getFieldValue(order, fieldName) {
        switch (fieldName) {
            case 'customerName':
                return order.customerName || '';
            case 'pickupDate':
                return order.pickupDate || '';
            case 'pickupTime':
                return order.pickupTime || '';
            case 'customerContact':
                return order.customerContact || '';
            case 'passengerCount':
                return order.passengerCount || 1;
            case 'luggageCount':
                return order.luggageCount || 0;
            case 'price':
                return order.price || '';
            case 'vehicleType':
                return order.carTypeId || 1;
            default:
                return '';
        }
    }

    /**
     * 创建字段输入框
     * @param {string} fieldName - 字段名
     * @param {*} currentValue - 当前值
     * @returns {HTMLElement} 输入元素
     */
    createFieldInput(fieldName, currentValue) {
        let inputElement;

        switch (fieldName) {
            case 'pickupDate':
                inputElement = document.createElement('input');
                inputElement.type = 'date';
                inputElement.value = currentValue;
                break;

            case 'pickupTime':
                inputElement = document.createElement('input');
                inputElement.type = 'time';
                inputElement.value = currentValue;
                break;

            case 'passengerCount':
            case 'luggageCount':
                inputElement = document.createElement('input');
                inputElement.type = 'number';
                inputElement.min = '0';
                inputElement.max = fieldName === 'passengerCount' ? '20' : '50';
                inputElement.value = currentValue;
                break;

            case 'price':
                inputElement = document.createElement('input');
                inputElement.type = 'number';
                inputElement.step = '0.01';
                inputElement.min = '0';
                inputElement.value = currentValue;
                break;

            case 'vehicleType':
                inputElement = document.createElement('select');
                const vehicleTypes = [
                    { id: 1, name: '舒适5座' },
                    { id: 2, name: '豪华轿车' },
                    { id: 3, name: 'MPV 7座' },
                    { id: 4, name: '商务车' }
                ];
                vehicleTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.name;
                    option.selected = type.id == currentValue;
                    inputElement.appendChild(option);
                });
                break;

            default:
                inputElement = document.createElement('input');
                inputElement.type = 'text';
                inputElement.value = currentValue;
                break;
        }

        inputElement.className = 'field-edit-input';
        return inputElement;
    }

    /**
     * 保存字段编辑
     * @param {HTMLElement} fieldElement - 字段元素
     * @param {HTMLElement} inputElement - 输入元素
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     */
    saveFieldEdit(fieldElement, inputElement, index, fieldName) {
        const newValue = inputElement.value;

        // 更新订单数据
        this.updateOrderField(index, fieldName, newValue);

        // 退出编辑状态
        this.exitFieldEdit(fieldElement, index, fieldName);

        // 重新渲染订单列表以反映更改
        this.renderOrderList();

        getLogger()?.log(`字段 ${fieldName} 已更新为: ${newValue}`, 'info');
    }

    /**
     * 退出字段编辑
     * @param {HTMLElement} fieldElement - 字段元素
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     */
    exitFieldEdit(fieldElement, index, fieldName) {
        // 移除编辑状态
        fieldElement.classList.remove('editing-field');

        // 移除输入框
        const inputElement = fieldElement.querySelector('.field-edit-input');
        if (inputElement) {
            inputElement.remove();
        }

        // 显示原始值元素
        const valueElement = fieldElement.querySelector('.grid-value');
        if (valueElement) {
            valueElement.style.display = '';
        }

        getLogger()?.log(`退出字段编辑: ${fieldName}`, 'info');
    }

    /**
     * 🔍 调试订单字段映射状态
     * @param {Object} order - 订单对象
     * @param {number} index - 订单索引
     */
    debugOrderFieldMapping(order, index) {
        const debugInfo = {
            orderIndex: index,
            originalFields: {},
            mappedFields: {},
            missingFields: [],
            fieldSources: {}
        };

        // 检查关键字段的存在和来源
        const keyFields = [
            'customerName', 'customerContact', 'pickupDate', 'pickupTime',
            'pickup', 'pickupLocation', 'dropoff', 'dropoffLocation',
            'passengerCount', 'luggageCount', 'price', 'otaPrice',
            'carTypeId', 'vehicleType', 'currency'
        ];

        keyFields.forEach(field => {
            if (order.hasOwnProperty(field)) {
                debugInfo.originalFields[field] = order[field];
                debugInfo.fieldSources[field] = 'direct';
            } else {
                debugInfo.missingFields.push(field);
            }
        });

        // 检查地址字段的映射情况
        debugInfo.addressMapping = {
            pickup: order.pickup || order.pickupLocation || '未找到',
            dropoff: order.dropoff || order.dropoffLocation || '未找到',
            pickupSource: order.pickup ? 'pickup' : order.pickupLocation ? 'pickupLocation' : 'none',
            dropoffSource: order.dropoff ? 'dropoff' : order.dropoffLocation ? 'dropoffLocation' : 'none'
        };

        // 检查价格字段的映射情况
        debugInfo.priceMapping = {
            price: order.price || '未找到',
            otaPrice: order.otaPrice || '未找到',
            currency: order.currency || 'MYR',
            priceSource: order.otaPrice ? 'otaPrice' : order.price ? 'price' : 'none'
        };

        // 检查联系方式字段
        debugInfo.contactMapping = {
            customerContact: order.customerContact || '未找到',
            phone: order.phone || '未找到',
            contactSource: order.customerContact ? 'customerContact' : order.phone ? 'phone' : 'none'
        };

        // 输出调试信息
        console.group(`🔍 订单字段映射调试 - 订单 #${index + 1}`);
        console.log('📋 原始订单对象:', order);
        console.log('🔍 字段映射分析:', debugInfo);
        console.log('❌ 缺失字段:', debugInfo.missingFields);
        console.log('📍 地址映射:', debugInfo.addressMapping);
        console.log('💰 价格映射:', debugInfo.priceMapping);
        console.log('📞 联系方式映射:', debugInfo.contactMapping);
        console.groupEnd();

        // 记录到日志系统
        getLogger()?.log(`订单 #${index + 1} 字段映射分析`, 'info', debugInfo);

        return debugInfo;
    }

    /**
     * 🔍 分析数据流转过程中的字段变化
     * @param {Array} originalOrders - Gemini AI返回的原始订单数据
     * @param {Array} processedOrders - 处理后的订单数据
     */
    analyzeDataFlowTransformation(originalOrders, processedOrders) {
        console.group('🔍 数据流转分析报告');

        const analysis = {
            originalCount: originalOrders?.length || 0,
            processedCount: processedOrders?.length || 0,
            fieldTransformations: [],
            dataLoss: [],
            fieldMappingIssues: []
        };

        if (!originalOrders || !processedOrders) {
            console.warn('❌ 缺少原始数据或处理后数据');
            console.groupEnd();
            return analysis;
        }

        // 逐个订单分析
        originalOrders.forEach((originalOrder, index) => {
            const processedOrder = processedOrders[index];
            if (!processedOrder) {
                analysis.dataLoss.push(`订单 #${index + 1}: 整个订单丢失`);
                return;
            }

            const orderAnalysis = {
                orderIndex: index + 1,
                fieldChanges: {},
                missingFields: [],
                newFields: []
            };

            // 检查字段变化
            const allFields = new Set([
                ...Object.keys(originalOrder),
                ...Object.keys(processedOrder)
            ]);

            allFields.forEach(field => {
                const originalValue = originalOrder[field];
                const processedValue = processedOrder[field];

                if (originalValue !== undefined && processedValue === undefined) {
                    orderAnalysis.missingFields.push(field);
                } else if (originalValue === undefined && processedValue !== undefined) {
                    orderAnalysis.newFields.push(field);
                } else if (originalValue !== processedValue) {
                    orderAnalysis.fieldChanges[field] = {
                        original: originalValue,
                        processed: processedValue
                    };
                }
            });

            analysis.fieldTransformations.push(orderAnalysis);
        });

        // 输出分析结果
        console.log('📊 数据流转统计:', {
            原始订单数: analysis.originalCount,
            处理后订单数: analysis.processedCount,
            数据完整性: analysis.processedCount === analysis.originalCount ? '✅ 完整' : '❌ 有丢失'
        });

        analysis.fieldTransformations.forEach(orderAnalysis => {
            console.group(`📋 订单 #${orderAnalysis.orderIndex} 字段变化分析`);

            if (orderAnalysis.missingFields.length > 0) {
                console.warn('❌ 丢失字段:', orderAnalysis.missingFields);
            }

            if (orderAnalysis.newFields.length > 0) {
                console.log('✅ 新增字段:', orderAnalysis.newFields);
            }

            if (Object.keys(orderAnalysis.fieldChanges).length > 0) {
                console.log('🔄 字段变化:', orderAnalysis.fieldChanges);
            }

            console.groupEnd();
        });

        console.groupEnd();

        // 记录到日志系统
        getLogger()?.log('数据流转分析完成', 'info', analysis);

        return analysis;
    }

    /**
     * 🔍 检查常见的字段显示问题
     * @param {Array} orders - 订单数组
     * @returns {Object} 问题分析报告
     */
    checkCommonFieldDisplayIssues(orders) {
        console.group('🔍 常见字段显示问题检查');

        const issues = {
            emptyFields: {},
            fieldMappingProblems: {},
            dataTypeIssues: {},
            recommendations: []
        };

        orders.forEach((order, index) => {
            const orderKey = `订单#${index + 1}`;

            // 检查空字段
            const emptyFields = [];
            const criticalFields = ['customerName', 'pickup', 'dropoff', 'pickupDate', 'pickupTime'];

            criticalFields.forEach(field => {
                const value = order[field] || order[this.getAlternativeFieldName(field)];
                if (!value || value === '未指定' || value === '未提供') {
                    emptyFields.push(field);
                }
            });

            if (emptyFields.length > 0) {
                issues.emptyFields[orderKey] = emptyFields;
            }

            // 检查字段映射问题
            const mappingProblems = [];

            // 地址字段映射检查
            if (!order.pickup && !order.pickupLocation) {
                mappingProblems.push('缺少上车地址字段 (pickup/pickupLocation)');
            }
            if (!order.dropoff && !order.dropoffLocation) {
                mappingProblems.push('缺少下车地址字段 (dropoff/dropoffLocation)');
            }

            // 价格字段映射检查
            if (!order.price && !order.otaPrice) {
                mappingProblems.push('缺少价格字段 (price/otaPrice)');
            }

            // 联系方式字段映射检查
            if (!order.customerContact && !order.phone) {
                mappingProblems.push('缺少联系方式字段 (customerContact/phone)');
            }

            if (mappingProblems.length > 0) {
                issues.fieldMappingProblems[orderKey] = mappingProblems;
            }

            // 检查数据类型问题
            const typeIssues = [];

            if (order.passengerCount && isNaN(parseInt(order.passengerCount))) {
                typeIssues.push('乘客数量不是有效数字');
            }
            if (order.luggageCount && isNaN(parseInt(order.luggageCount))) {
                typeIssues.push('行李数量不是有效数字');
            }
            if ((order.price || order.otaPrice) && isNaN(parseFloat(order.price || order.otaPrice))) {
                typeIssues.push('价格不是有效数字');
            }

            if (typeIssues.length > 0) {
                issues.dataTypeIssues[orderKey] = typeIssues;
            }
        });

        // 生成修复建议
        if (Object.keys(issues.emptyFields).length > 0) {
            issues.recommendations.push('建议：检查Gemini AI解析结果，确保关键字段被正确提取');
        }
        if (Object.keys(issues.fieldMappingProblems).length > 0) {
            issues.recommendations.push('建议：检查字段映射逻辑，确保备用字段名被正确处理');
        }
        if (Object.keys(issues.dataTypeIssues).length > 0) {
            issues.recommendations.push('建议：添加数据类型验证和转换逻辑');
        }

        // 输出检查结果
        console.log('📊 字段显示问题统计:', {
            空字段问题: Object.keys(issues.emptyFields).length,
            映射问题: Object.keys(issues.fieldMappingProblems).length,
            类型问题: Object.keys(issues.dataTypeIssues).length
        });

        if (Object.keys(issues.emptyFields).length > 0) {
            console.warn('❌ 空字段问题:', issues.emptyFields);
        }
        if (Object.keys(issues.fieldMappingProblems).length > 0) {
            console.warn('❌ 字段映射问题:', issues.fieldMappingProblems);
        }
        if (Object.keys(issues.dataTypeIssues).length > 0) {
            console.warn('❌ 数据类型问题:', issues.dataTypeIssues);
        }

        console.log('💡 修复建议:', issues.recommendations);
        console.groupEnd();

        // 记录到日志系统
        getLogger()?.log('字段显示问题检查完成', 'info', issues);

        return issues;
    }

    /**
     * 获取字段的备用名称
     * @param {string} fieldName - 字段名
     * @returns {string} 备用字段名
     */
    getAlternativeFieldName(fieldName) {
        const alternatives = {
            'pickup': 'pickupLocation',
            'dropoff': 'dropoffLocation',
            'customerContact': 'phone',
            'price': 'otaPrice'
        };
        return alternatives[fieldName] || fieldName;
    }

    /**
     * 🔍 生成订单内容字段显示问题排查报告
     * @param {Array} originalOrders - 原始订单数据
     * @param {Array} processedOrders - 处理后订单数据
     * @returns {Object} 完整的排查报告
     */
    generateFieldDisplayDiagnosticReport(originalOrders, processedOrders) {
        console.group('📋 订单内容字段显示问题排查报告');

        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalOrders: processedOrders?.length || 0,
                issuesFound: 0,
                criticalIssues: 0,
                recommendations: []
            },
            dataFlowAnalysis: null,
            fieldDisplayIssues: null,
            detailedFindings: [],
            actionItems: []
        };

        try {
            // 1. 数据流转分析
            console.log('🔍 步骤1: 数据流转分析');
            report.dataFlowAnalysis = this.analyzeDataFlowTransformation(originalOrders, processedOrders);

            // 2. 字段显示问题检查
            console.log('🔍 步骤2: 字段显示问题检查');
            report.fieldDisplayIssues = this.checkCommonFieldDisplayIssues(processedOrders);

            // 3. 详细问题分析
            console.log('🔍 步骤3: 详细问题分析');
            processedOrders.forEach((order, index) => {
                const orderFindings = this.analyzeIndividualOrderIssues(order, index);
                if (orderFindings.issues.length > 0) {
                    report.detailedFindings.push(orderFindings);
                }
            });

            // 4. 统计问题数量
            report.summary.issuesFound = report.detailedFindings.length;
            report.summary.criticalIssues = report.detailedFindings.filter(f => f.severity === 'critical').length;

            // 5. 生成修复建议
            report.actionItems = this.generateFixRecommendations(report);

            // 6. 输出报告摘要
            console.log('📊 排查报告摘要:', {
                订单总数: report.summary.totalOrders,
                发现问题: report.summary.issuesFound,
                严重问题: report.summary.criticalIssues,
                修复建议数: report.actionItems.length
            });

            if (report.summary.issuesFound > 0) {
                console.warn('❌ 发现字段显示问题，请查看详细报告');
            } else {
                console.log('✅ 未发现字段显示问题');
            }

        } catch (error) {
            console.error('❌ 生成排查报告时出错:', error);
            report.error = error.message;
        }

        console.groupEnd();

        // 记录完整报告到日志系统
        getLogger()?.log('订单字段显示问题排查报告', 'info', report);

        return report;
    }

    /**
     * 分析单个订单的问题
     * @param {Object} order - 订单对象
     * @param {number} index - 订单索引
     * @returns {Object} 订单问题分析结果
     */
    analyzeIndividualOrderIssues(order, index) {
        const findings = {
            orderIndex: index + 1,
            issues: [],
            severity: 'normal',
            affectedFields: []
        };

        // 检查关键字段缺失
        const criticalFields = ['customerName', 'pickup', 'dropoff'];
        criticalFields.forEach(field => {
            const value = order[field] || order[this.getAlternativeFieldName(field)];
            if (!value || value === '未指定' || value === '未提供') {
                findings.issues.push(`关键字段 ${field} 缺失或为空`);
                findings.affectedFields.push(field);
                findings.severity = 'critical';
            }
        });

        // 检查数据格式问题
        if (order.pickupDate && !/^\d{4}-\d{2}-\d{2}$/.test(order.pickupDate)) {
            findings.issues.push('日期格式不正确');
            findings.affectedFields.push('pickupDate');
        }

        if (order.pickupTime && !/^\d{2}:\d{2}$/.test(order.pickupTime)) {
            findings.issues.push('时间格式不正确');
            findings.affectedFields.push('pickupTime');
        }

        return findings;
    }

    /**
     * 生成修复建议
     * @param {Object} report - 排查报告
     * @returns {Array} 修复建议列表
     */
    generateFixRecommendations(report) {
        const recommendations = [];

        if (report.summary.criticalIssues > 0) {
            recommendations.push({
                priority: 'high',
                action: '修复关键字段缺失问题',
                description: '检查Gemini AI解析逻辑，确保关键字段被正确提取和映射'
            });
        }

        if (report.fieldDisplayIssues && Object.keys(report.fieldDisplayIssues.fieldMappingProblems).length > 0) {
            recommendations.push({
                priority: 'medium',
                action: '优化字段映射逻辑',
                description: '实现更智能的字段名映射，支持多种可能的字段名变体'
            });
        }

        if (report.fieldDisplayIssues && Object.keys(report.fieldDisplayIssues.dataTypeIssues).length > 0) {
            recommendations.push({
                priority: 'medium',
                action: '添加数据类型验证',
                description: '在显示前对数据进行类型检查和格式化处理'
            });
        }

        return recommendations;
    }

    /**
     * 🔧 创建快捷编辑面板（修复内存泄漏）
     * @param {number} index - 订单索引
     */
    createQuickEditPanel(index) {
        // 先清理现有的快捷编辑面板
        this.cleanupQuickEditPanels();

        const order = this.state.parsedOrders[index];
        if (!order) {
            getLogger()?.log(`订单 ${index} 不存在，无法创建快捷编辑面板`, 'warn');
            return;
        }
        
        // 创建覆盖层
        const overlay = document.createElement('div');
        overlay.className = 'quick-edit-overlay';
        overlay.setAttribute('data-order-index', index.toString());
        
        // 创建编辑面板
        const panel = document.createElement('div');
        panel.className = 'quick-edit-panel';
        
        // 常用字段的快捷编辑
        const quickFields = [
            { name: 'customerName', label: '客户姓名', type: 'text', required: true },
            { name: 'customerContact', label: '联系电话', type: 'tel', required: false },
            { name: 'pickup', label: '上车地点', type: 'text', required: true },
            { name: 'dropoff', label: '目的地', type: 'text', required: true },
            { name: 'pickupDate', label: '接送日期', type: 'date', required: true },
            { name: 'pickupTime', label: '接送时间', type: 'time', required: false },
            { name: 'otaReferenceNumber', label: 'OTA参考号', type: 'text', required: true }
        ];

        let fieldsHTML = quickFields.map(field => {
            const value = order[field.name] || '';
            const isOtaRef = field.name === 'otaReferenceNumber';
            const hasValue = value && value.toString().trim() !== '';
            const isRequired = field.required;
            const isInvalid = isRequired && !hasValue;
            
            return `
                <div class="order-field-group ${isInvalid ? 'invalid' : ''}" data-field="${field.name}" data-required="${isRequired}">
                    <label class="order-field-label">${field.label}${isRequired ? ' *' : ''}</label>
                    <div class="${isOtaRef ? 'ota-reference-group' : 'field-input-wrapper'}">
                        <input 
                            type="${field.type}" 
                            name="${field.name}" 
                            value="${this.escapeHtml(value)}" 
                            class="order-field-input ${isOtaRef && !hasValue ? 'ota-reference-missing' : ''}"
                            data-field="${field.name}"
                            data-index="${index}"
                            onblur="window.OTA.multiOrderManager.handleQuickEditBlur(event, ${index}, '${field.name}')"
                            oninput="window.OTA.multiOrderManager.handleQuickEditInput(event, ${index}, '${field.name}')"
                            onkeypress="window.OTA.multiOrderManager.handleQuickEditKeypress(event)">
                        ${isOtaRef && !hasValue ? `
                            <button type="button" class="ota-reference-generate-btn" 
                                    onclick="window.OTA.multiOrderManager.generateOtaReference(${index})" 
                                    title="生成随机参考号">🎲</button>
                        ` : ''}
                    </div>
                    <div class="field-validation-container"></div>
                </div>
            `;
        }).join('');

        panel.innerHTML = `
            <div class="quick-edit-header">
                <h3 class="quick-edit-title">快速编辑订单 ${index + 1}</h3>
                <button class="quick-edit-close" onclick="window.OTA.multiOrderManager.exitQuickEdit(${index})">✕</button>
            </div>
            <div class="quick-edit-fields">
                ${fieldsHTML}
            </div>
            <div class="quick-edit-actions">
                <button class="btn btn-outline" onclick="window.OTA.multiOrderManager.exitQuickEdit(${index})">取消</button>
                <button class="btn btn-primary" onclick="window.OTA.multiOrderManager.saveQuickEdit(${index})">保存</button>
            </div>
        `;

        overlay.appendChild(panel);
        document.body.appendChild(overlay);

        // 标记为使用中，防止被定期清理误删
        overlay.classList.add('in-use');

        // 显示面板动画
        requestAnimationFrame(() => {
            panel.classList.add('show');
        });

        // 为输入框添加事件监听器（使用绑定的方法避免内存泄漏）
        const inputs = panel.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                input.select();
            });
        });

        // 点击覆盖层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.exitQuickEdit(index);
            }
        });

        // 添加键盘事件监听
        document.addEventListener('keydown', this.handleQuickEditEscape);

        getLogger()?.log(`创建快捷编辑面板完成: 订单 ${index + 1}`, 'info');
    }

    /**
     * 🔧 HTML转义函数（防止XSS）
     */
    escapeHtml(unsafe) {
        if (typeof unsafe !== 'string') return unsafe;
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    /**
     * 🔧 快捷编辑事件处理器（修复内存泄漏）
     */
    handleQuickEditBlur(event, index, fieldName) {
        const value = event.target.value;
        this.saveFieldEdit(index, fieldName, value);
        this.updateOrderField(index, fieldName, value);
        this.validateField(index, fieldName, value);
    }

    handleQuickEditInput(event, index, fieldName) {
        const value = event.target.value;
        this.updateOrderField(index, fieldName, value);
        this.validateField(index, fieldName, value);
    }

    handleQuickEditKeypress(event) {
        if (event.key === 'Enter') {
            event.target.blur();
        }
    }

    handleQuickEditEscape(event) {
        if (event.key === 'Escape') {
            const overlay = document.querySelector('.quick-edit-overlay');
            if (overlay) {
                const index = overlay.getAttribute('data-order-index');
                if (index) {
                    window.OTA.multiOrderManager.exitQuickEdit(parseInt(index));
                }
            }
        }
    }

    /**
     * 退出快捷编辑
     * @param {number} index - 订单索引
     */
    exitQuickEdit(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (orderItem) {
            orderItem.classList.remove('editing');
        }

        // 使用专门的清理方法来移除快捷编辑面板
        this.cleanupQuickEditPanels();

        getLogger()?.log(`退出订单 ${index + 1} 快捷编辑模式`, 'info');
    }
    handleQuickEditKeypress(event) {
        if (event.key === 'Enter') {
            event.target.blur();
        }
    }

    /**
     * 保存快捷编辑
     * @param {number} index - 订单索引
     */
    saveQuickEdit(index) {
        // 验证所有必填字段
        const requiredFields = ['customerName', 'pickup', 'dropoff', 'pickupDate', 'otaReferenceNumber'];
        const order = this.state.parsedOrders[index];
        let hasErrors = false;

        for (const fieldName of requiredFields) {
            const value = order[fieldName];
            if (!value || value.toString().trim() === '') {
                hasErrors = true;
                break;
            }
        }

        if (hasErrors) {
            // 显示错误提示
            const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
            if (orderItem) {
                orderItem.classList.add('invalid');
                setTimeout(() => {
                    orderItem.classList.remove('invalid');
                }, 2000);
            }
            
            getLogger()?.log(`订单 ${index + 1} 存在必填字段缺失，无法保存`, 'warn');
            return;
        }

        // 保存成功，退出编辑模式
        this.exitQuickEdit(index);
        getLogger()?.log(`订单 ${index + 1} 快捷编辑已保存`, 'success');
    }

    /**
     * 更新订单摘要显示
     * @param {number} index - 订单索引
     */
    updateOrderSummaryDisplay(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem || !this.state.parsedOrders[index]) return;

        const order = this.state.parsedOrders[index];
        const summaryDiv = orderItem.querySelector('.order-summary');
        
        if (summaryDiv) {
            // 修复：传递正确的索引参数到generateOrderSummary
            summaryDiv.innerHTML = this.generateOrderSummary(order, index);
        }
    }

    /**
     * 获取OTA渠道选项HTML
     * @returns {string} OTA渠道选项HTML
     */
    getOtaChannelOptions() {
        // 从ota-channel-mapping.js模组获取渠道数据
        const appState = getAppState();
        const currentUser = appState?.get ? appState.get('auth.user') : null;
        let otaChannels = [];

        try {
            // 优先获取当前用户的特定渠道配置
            if (currentUser && window.OTA?.otaChannelMapping?.getConfig) {
                const userConfig = window.OTA.otaChannelMapping.getConfig(currentUser.id) || 
                                 window.OTA.otaChannelMapping.getConfig(currentUser.email);
                
                if (userConfig && userConfig.options) {
                    // 使用用户特定的渠道配置
                    otaChannels = userConfig.options;
                    getLogger()?.log(`使用用户特定OTA渠道配置`, 'info', { 
                        userId: currentUser.id, 
                        channelCount: otaChannels.length 
                    });
                }
            }

            // 如果没有用户特定配置，使用通用渠道列表
            if (otaChannels.length === 0 && window.OTA?.otaChannelMapping?.commonChannels) {
                otaChannels = window.OTA.otaChannelMapping.commonChannels;
                getLogger()?.log(`使用通用OTA渠道列表`, 'info', { 
                    channelCount: otaChannels.length 
                });
            }

            // 如果仍然没有数据，使用fallback列表
            if (otaChannels.length === 0) {
                otaChannels = [
                    { value: 'Ctrip', text: '携程' },
                    { value: 'Klook', text: 'Klook客路' },
                    { value: 'KKday', text: 'KKday' },
                    { value: 'Other', text: '其他' }
                ];
                getLogger()?.log(`OTA渠道模组未加载，使用fallback列表`, 'warn');
            }

        } catch (error) {
            getLogger()?.logError('获取OTA渠道配置时出错', error);
            // 错误时使用基本列表
            otaChannels = [
                { value: 'Ctrip', text: '携程' },
                { value: 'Klook', text: 'Klook客路' },
                { value: 'KKday', text: 'KKday' },
                { value: 'Other', text: '其他' }
            ];
        }
        
        return otaChannels.map(channel => 
            `<option value="${channel.value}">${channel.text}</option>`
        ).join('');
    }

    /**
     * 获取语言选择组件HTML (下拉菜单+多选择tick box方式)
     * @returns {string} 语言选择组件HTML
     */
    getLanguageSelectionComponent() {
        // **修复**: 使用与单订单相同的语言数据源，添加更好的错误处理
        const apiService = getApiService();
        let languages = [];

        try {
            // 尝试从API服务获取语言数据
            if (apiService && apiService.staticData && apiService.staticData.languages) {
                languages = apiService.staticData.languages;
                getLogger().log('✅ 多订单管理器：使用API服务语言数据', 'info', {
                    count: languages.length
                });
            } else {
                // 尝试从应用状态获取语言数据
                const appState = getAppState();
                const systemData = appState ? appState.getSystemData() : null;

                if (systemData && systemData.languages) {
                    languages = systemData.languages;
                    getLogger().log('✅ 多订单管理器：使用应用状态语言数据', 'info', {
                        count: languages.length
                    });
                } else {
                    // 使用统一语言管理器获取数据
                    const languageManager = getLanguageManager();
                    languages = languageManager.getLanguagesSync({ enabledOnly: true });
                    getLogger().log('⚠️ 多订单管理器：使用统一语言管理器数据', 'warn');
                }
            }
        } catch (error) {
            getLogger().logError('多订单管理器：获取语言数据失败', {
                error: error.message
            });
            // 使用统一语言管理器的备用数据
            try {
                const languageManager = getLanguageManager();
                languages = languageManager.getLanguagesSync({ enabledOnly: true });
            } catch (fallbackError) {
                // 如果统一管理器也失败，使用最基础的数据
                languages = [
                    { id: 2, name: 'English (EN)' },
                    { id: 3, name: 'Malay (MY)' },
                    { id: 4, name: 'Chinese (CN)' }
                ];
            }
        }
        
        // **修复**: 添加国际化支持
        const i18nManager = this.getI18nManagerWithRetry();
        const placeholderText = i18nManager ?
            i18nManager.t('form.selectLanguages') : '选择语言...';

        return `
            <div class="language-selection-wrapper">
                <div class="language-dropdown" id="batchLanguageDropdown">
                    <div class="language-dropdown-header" onclick="window.OTA.multiOrderManager.toggleLanguageDropdown()">
                        <span id="languageSelectedText" data-i18n="form.selectLanguages">${placeholderText}</span>
                        <span class="dropdown-arrow">▼</span>
                    </div>
                    <div class="language-dropdown-content" id="languageDropdownContent" style="display: none;">
                        ${languages.map(lang => `
                            <label class="language-checkbox-item">
                                <input type="checkbox" value="${lang.id}" onchange="window.OTA.multiOrderManager.updateLanguageSelection()">
                                <span class="language-name">${lang.name}</span>
                            </label>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定批量控制面板事件
     */
    bindBatchControlEvents() {
        
        // 绑定批量控制面板toggle按钮
        const toggleBtn = document.querySelector('.batch-control-toggle');
        if (toggleBtn) {
            // 避免重复绑定
            toggleBtn.removeEventListener('click', this.toggleBatchControlPanel);
            toggleBtn.addEventListener('click', () => this.toggleBatchControlPanel());
        }

        // 绑定OTA渠道变化事件，实现联动效果
        const otaChannelSelect = document.getElementById('batchOtaChannel');
        if (otaChannelSelect) {
            otaChannelSelect.addEventListener('change', (e) => {
                if (e.target.value) {
                    this.logger?.log(`选择了OTA渠道: ${e.target.value}`, 'info');
                }
            });
        }

        // 绑定语言选择变化事件
        const languageSelect = document.getElementById('batchLanguage');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                const selectedOptions = Array.from(e.target.selectedOptions);
                if (selectedOptions.length > 0) {
                    const languages = selectedOptions.map(opt => opt.text).join(', ');
                    this.logger?.log(`选择了语言: ${languages}`, 'info');
                }
            });
        }

        // 为服务类型选择添加智能提示
        const serviceTypeSelect = document.getElementById('batchServiceType');
        if (serviceTypeSelect) {
            serviceTypeSelect.addEventListener('change', (e) => {
                const serviceId = parseInt(e.target.value);
                if (serviceId) {
                    const serviceName = this.getServiceTypeName(serviceId);
                    this.logger?.log(`选择了服务类型: ${serviceName}`, 'info');
                    
                    // 根据服务类型自动推荐合适的区域
                    this.autoSuggestRegionForService(serviceId);
                }
            });
        }

        // 为车型选择添加容量提示
        const carTypeSelect = document.getElementById('batchCarType');
        if (carTypeSelect) {
            carTypeSelect.addEventListener('change', (e) => {
                const carTypeId = parseInt(e.target.value);
                if (carTypeId) {
                    const carTypeName = this.getCarTypeName(carTypeId);
                    this.logger?.log(`选择了车型: ${carTypeName}`, 'info');
                }
            });
        }

        // 为区域选择添加说明
        const regionSelect = document.getElementById('batchRegion');
        if (regionSelect) {
            regionSelect.addEventListener('change', (e) => {
                const regionId = parseInt(e.target.value);
                if (regionId) {
                    const regionName = this.getRegionName(regionId);
                    this.logger?.log(`选择了服务区域: ${regionName}`, 'info');
                }
            });
        }

        this.logger?.log('✅ 批量控制面板事件绑定完成', 'success');
    }

    /**
     * 根据服务类型自动推荐合适的区域
     * @param {number} serviceId - 服务类型ID
     */
    autoSuggestRegionForService(serviceId) {
        // 举牌服务自动推荐举牌区域
        if (serviceId === 5) { // 举牌服务
            const regionSelect = document.getElementById('batchRegion');
            if (regionSelect) {
                regionSelect.value = '9'; // 举牌服务区域
                this.updateBatchStatus('已自动推荐举牌服务区域');
            }
        }
    }

    /**
     * 应用批量OTA渠道设置
     */
    applyBatchOtaChannel() {
        const select = document.getElementById('batchOtaChannel');
        const selectedChannel = select?.value;
        
        if (!selectedChannel) {
            getLogger()?.log('请先选择OTA渠道', 'warn');
            return;
        }

        const selectedOrders = this.getSelectedOrderIndexes();
        if (selectedOrders.length === 0) {
            getLogger()?.log('请先选择要批量设置的订单', 'warn');
            return;
        }

        selectedOrders.forEach(index => {
            this.updateOrderField(index, 'ota', selectedChannel);
        });

        this.updateBatchStatus(`已为 ${selectedOrders.length} 个订单设置OTA渠道: ${selectedChannel}`);
        getLogger()?.log(`批量设置OTA渠道: ${selectedChannel}`, 'success', { count: selectedOrders.length });
    }

    /**
     * 应用批量语言设置
     */
    applyBatchLanguage() {
        // 从新的语言选择组件中获取选中的语言
        const checkboxes = document.querySelectorAll('#languageDropdownContent input[type="checkbox"]:checked');
        const selectedLanguages = Array.from(checkboxes).map(checkbox => parseInt(checkbox.value));
        
        if (selectedLanguages.length === 0) {
            getLogger()?.log('请先选择语言', 'warn');
            return;
        }

        const selectedOrders = this.getSelectedOrderIndexes();
        if (selectedOrders.length === 0) {
            getLogger()?.log('请先选择要批量设置的订单', 'warn');
            return;
        }

        selectedOrders.forEach(index => {
            this.updateOrderField(index, 'languagesIdArray', selectedLanguages);
        });

        // 获取语言名称用于显示
        const languageNames = this.getSelectedLanguageNames(selectedLanguages);
        this.updateBatchStatus(`已为 ${selectedOrders.length} 个订单设置语言: ${languageNames.join(', ')}`);
        getLogger()?.log(`批量设置语言`, 'success', { count: selectedOrders.length, languages: selectedLanguages });
    }

    /**
     * 应用批量区域设置
     */
    applyBatchRegion() {
        const select = document.getElementById('batchRegion');
        const selectedRegion = parseInt(select?.value);
        
        if (!selectedRegion) {
            getLogger()?.log('请先选择服务区域', 'warn');
            return;
        }

        const selectedOrders = this.getSelectedOrderIndexes();
        if (selectedOrders.length === 0) {
            getLogger()?.log('请先选择要批量设置的订单', 'warn');
            return;
        }

        selectedOrders.forEach(index => {
            this.updateOrderField(index, 'drivingRegionId', selectedRegion);
        });

        const regionName = this.getRegionName(selectedRegion);
        this.updateBatchStatus(`已为 ${selectedOrders.length} 个订单设置服务区域: ${regionName}`);
        getLogger()?.log(`批量设置服务区域: ${regionName}`, 'success', { count: selectedOrders.length });
    }

    /**
     * 清除批量设置
     */
    clearBatchSettings() {
        const selects = [
            'batchOtaChannel',
            'batchLanguage', 
            'batchServiceType',
            'batchCarType',
            'batchRegion'
        ];

        selects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                if (select.multiple) {
                    Array.from(select.options).forEach(option => option.selected = false);
                } else {
                    select.value = '';
                }
            }
        });

        this.updateBatchStatus('批量设置已清除');
        getLogger()?.log('批量设置已清除', 'info');
    }

    /**
     * 重置所有订单
     */
    resetAllOrders() {
        if (!this.state.parsedOrders || this.state.parsedOrders.length === 0) {
            getLogger()?.log('没有可重置的订单', 'warn');
            return;
        }

        // 确认重置操作
        if (!confirm('确定要重置所有订单的编辑吗？这将撤销所有手动修改。')) {
            return;
        }

        // 这里可以实现重置逻辑，比如从原始数据恢复
        // 暂时先显示提示
        this.updateBatchStatus('订单重置功能正在开发中');
        getLogger()?.log('订单重置功能正在开发中', 'info');
    }

    /**
     * 切换批量控制面板显示/隐藏
     */
    toggleBatchControlPanel() {
        const content = document.querySelector('.batch-control-content');
        const toggleIcon = document.querySelector('.batch-control-toggle .toggle-icon');
        
        if (content && toggleIcon) {
            const isHidden = content.style.display === 'none';
            content.style.display = isHidden ? 'block' : 'none';
            toggleIcon.textContent = isHidden ? '📐' : '📊';
        }
    }

    /**
     * 获取选中订单的索引数组
     * @returns {Array<number>} 选中订单的索引数组
     */
    getSelectedOrderIndexes() {
        const checkboxes = document.querySelectorAll('.order-checkbox:checked');
        return Array.from(checkboxes).map(checkbox => {
            const orderItem = checkbox.closest('.order-card');
            return parseInt(orderItem.getAttribute('data-order-index'));
        }).filter(index => !isNaN(index));
    }

    /**
     * 更新批量状态显示
     * @param {string} message - 状态消息
     */
    updateBatchStatus(message) {
        const statusElement = document.getElementById('batchStatusText');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.style.color = '#28a745';
            
            // 3秒后恢复默认消息
            setTimeout(() => {
                statusElement.textContent = '选择设置项并点击应用按钮来批量修改订单';
                statusElement.style.color = '';
            }, 3000);
        }
    }

    /**
     * 确保面板在视窗范围内可见
     */
    ensurePanelVisible() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (!multiOrderPanel) return;

        // 确保面板居中显示在视窗中
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // 获取面板的实际尺寸
        const panelRect = multiOrderPanel.getBoundingClientRect();
        
        // 如果面板超出视窗范围，调整其大小
        let adjustments = {};
        
        if (panelRect.width > viewportWidth * 0.95) {
            adjustments.width = '95vw';
        }
        
        if (panelRect.height > viewportHeight * 0.9) {
            adjustments.height = '90vh';
        }
        
        // 应用调整
        Object.assign(multiOrderPanel.style, adjustments);
        
        // 滚动到面板位置（如果需要）
        multiOrderPanel.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center',
            inline: 'center'
        });
        
        getLogger()?.log('📱 多订单面板位置已优化', 'info', {
            viewport: `${viewportWidth}x${viewportHeight}`,
            panel: `${panelRect.width}x${panelRect.height}`,
            adjustments
        });
    }

    /**
     * 添加面板拖拽功能（增强浮窗体验）
     */
    addPanelDragFeature() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        const header = multiOrderPanel?.querySelector('.multi-order-header');
        
        if (!multiOrderPanel || !header) return;
        
        // 检查是否已经添加了拖拽功能，避免重复绑定
        if (header.dataset.dragEnabled === 'true') {
            getLogger()?.log('🔒 面板拖拽功能已存在，跳过重复绑定', 'info');
            return;
        }

        let isDragging = false;
        let currentX = 0;
        let currentY = 0;
        let initialX = 0;
        let initialY = 0;

        header.style.cursor = 'move';
        header.title = '拖拽面板来移动位置';

        const dragStart = (e) => {
            if (e.target.closest('button')) return; // 避免按钮干扰拖拽
            
            initialX = e.clientX - currentX;
            initialY = e.clientY - currentY;
            
            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
                multiOrderPanel.style.transition = 'none';
            }
        };

        const dragEnd = (e) => {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            multiOrderPanel.style.transition = 'all var(--transition-normal)';
        };

        const drag = (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                
                // 限制拖拽范围在视窗内
                const rect = multiOrderPanel.getBoundingClientRect();
                const maxX = window.innerWidth - rect.width;
                const maxY = window.innerHeight - rect.height;
                
                currentX = Math.max(0, Math.min(currentX, maxX));
                currentY = Math.max(0, Math.min(currentY, maxY));
                
                multiOrderPanel.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        };

        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
        
        // 标记拖拽功能已启用
        header.dataset.dragEnabled = 'true';

        getLogger()?.log('🖱️ 面板拖拽功能已启用', 'info');
    }


    // **修复**: 删除重复的方法定义，保留第1036行和第1051行的版本

    /**
     * 根据语言ID获取语言名称
     * @param {number[]} languageIds - 语言ID数组
     * @returns {string[]} 语言名称数组
     */
    getSelectedLanguageNames(languageIds) {
        try {
            const languageManager = getLanguageManager();
            const languages = languageManager.getLanguagesSync();
            
            return languageIds.map(id => {
                const lang = languages.find(l => l.id === id);
                return lang ? lang.name : `语言${id}`;
            });
        } catch (error) {
            getLogger().logError('获取语言名称失败', error);
            return languageIds.map(id => `语言${id}`);
        }
    }

    /**
     * 🔧 设置清理机制（防止内存泄漏）
     */
    setupCleanupMechanism() {
        
        // 保存事件处理器引用
        this.boundEventHandlers.set('multiOrderDetected', (event) => {
            try {
                console.group('🔍 多订单数据流追踪 - 第5步：事件接收');
                const { multiOrderResult, orderText } = event.detail;
                
                console.log('事件类型:', event.type);
                console.log('事件详情:', event.detail);
                console.log('多订单结果:', multiOrderResult);
                console.log('订单文本长度:', orderText?.length);
                console.log('订单数量:', multiOrderResult?.orderCount);
                console.log('是否多订单:', multiOrderResult?.isMultiOrder);
                console.log('订单数据:', multiOrderResult?.orders);
                console.groupEnd();
                
                this.logger?.log('🔔 收到多订单检测事件（统一入口）', 'info', { 
                    hasMultiOrderResult: !!multiOrderResult,
                    orderCount: multiOrderResult?.orderCount || 0,
                    isMultiOrder: multiOrderResult?.isMultiOrder || false,
                    hasOrderText: !!orderText 
                });
                
                console.log('🔄 即将调用handleMultiOrderDetectionUnified...');
                this.handleMultiOrderDetectionUnified(multiOrderResult, orderText);
            } catch (error) {
                console.error('❌ 多订单事件处理失败:', error);
                this.logger?.logError('多订单事件处理失败', error);
            }
        });
        
        this.boundEventHandlers.set('appStateChanged', (event) => {
            try {
                if (event.detail.key === 'currentOrder') {
                    this.handleOrderStateChange(event.detail.value);
                }
            } catch (error) {
                this.logger?.logError('应用状态变化处理失败', error);
            }
        });
        
        // 设置页面卸载时的清理
        this.boundEventHandlers.set('beforeunload', () => {
            this.cleanup();
        });
        
        // 设置定期清理机制（防止内存泄漏）
        this.boundEventHandlers.set('periodicCleanup', () => {
            // 每30秒清理一次未使用的资源
            this.periodicCleanup();
        });
        
        // 绑定事件监听器
        document.addEventListener('multiOrderDetected', this.boundEventHandlers.get('multiOrderDetected'));
        document.addEventListener('appStateChanged', this.boundEventHandlers.get('appStateChanged'));
        window.addEventListener('beforeunload', this.boundEventHandlers.get('beforeunload'));
        
        // 设置定期清理（使用防抖处理）
        this.startPeriodicCleanup();
        
        this.logger?.log('🔧 多订单管理器清理机制已设置', 'info');
    }

    /**
     * 🔧 启动定期清理机制
     */
    startPeriodicCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        
        // 每30秒执行一次清理
        this.cleanupInterval = setInterval(() => {
            this.periodicCleanup();
        }, 30000);
        
        getLogger()?.log('🕐 定期清理机制已启动（30秒间隔）', 'info');
    }

    /**
     * 🔧 定期清理未使用资源
     */
    periodicCleanup() {
        try {
                
            // 清理过期的处理状态
            const now = Date.now();
            const maxAge = 5 * 60 * 1000; // 5分钟
            
            for (const [key, processedOrder] of this.state.processedOrders.entries()) {
                if (processedOrder.timestamp && (now - processedOrder.timestamp) > maxAge) {
                    this.state.processedOrders.delete(key);
                    this.logger?.log(`清理过期处理状态: ${key}`, 'debug');
                }
            }
            
            // 清理未使用的快捷编辑面板
            const unusedOverlays = document.querySelectorAll('.quick-edit-overlay:not(.in-use)');
            unusedOverlays.forEach(overlay => {
                if (!overlay.querySelector('.quick-edit-panel.show')) {
                    overlay.remove();
                }
            });
            
            // 清理空的状态数组
            if (this.state.currentSegments.length === 0) {
                this.state.currentSegments = [];
            }
            
            this.logger?.log('🧹 定期清理完成', 'debug');
        } catch (error) {
            getLogger()?.logError('定期清理失败', error);
        }
    }

    /**
     * 🔧 停止定期清理机制
     */
    stopPeriodicCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
            getLogger()?.log('🛑 定期清理机制已停止', 'info');
        }
    }

    /**
     * 🔧 标准化订单数据（与单订单处理逻辑同步）
     * 复制FormManager.collectFormData()的核心逻辑
     * @param {object} rawOrderData - 原始订单数据
     * @returns {object} 标准化后的订单数据
     */
    standardizeOrderData(rawOrderData) {
        const data = { ...rawOrderData }; // 复制原始数据
        
        this.logger?.log('🔧 开始标准化订单数据', 'info', { rawData: rawOrderData });
        
        // 🔧 修复：字段名映射（与单订单保持一致）
        const fieldMapping = {
            'customerName': 'customer_name',
            'customerContact': 'customer_contact', 
            'customerEmail': 'customer_email',
            'pickup': 'pickup_location',
            'dropoff': 'dropoff_location',
            'pickupDate': 'pickup_date',
            'pickupTime': 'pickup_time',
            'passengerCount': 'passenger_number',
            'luggageCount': 'luggage_count',
            'flightInfo': 'flight_number',
            'otaPrice': 'ota_price',
            'currency': 'currency',
            'otaReferenceNumber': 'ota_reference_number',
            'extraRequirement': 'extra_requirement',
            'remark': 'remark',
            'subCategoryId': 'sub_category_id',
            'carTypeId': 'car_type_id',
            'inchargeByBackendUserId': 'incharge_by_backend_user_id',
            'drivingRegionId': 'driving_region_id'
        };
        
        // 应用字段映射
        for (const [originalField, apiField] of Object.entries(fieldMapping)) {
            if (data.hasOwnProperty(originalField)) {
                data[apiField] = data[originalField];
                if (originalField !== apiField) {
                    delete data[originalField]; // 清理旧字段名
                }
            }
        }
        
        // 🔧 修复：处理语言数组（languagesIdArray → languages_id_array）- 使用统一管理器
        if (data.languagesIdArray && Array.isArray(data.languagesIdArray)) {
            try {
                const languageManager = getLanguageManager();
                data.languages_id_array = languageManager.transformForAPISync(data.languagesIdArray);
                delete data.languagesIdArray; // 清理旧字段名
            } catch (error) {
                getLogger().logError('多订单语言数据转换失败', error);
                // 使用fallback转换
                const languagesObject = {};
                data.languagesIdArray.forEach((langId, index) => {
                    languagesObject[index.toString()] = langId.toString();
                });
                data.languages_id_array = languagesObject;
                delete data.languagesIdArray;
            }
        }
        
        // 🔧 修复：确保必填字段有默认值（与单订单逻辑一致）
        if (!data.incharge_by_backend_user_id) {
            const defaultBackendUserId = getApiService()?.getDefaultBackendUserId();
            if (defaultBackendUserId) {
                data.incharge_by_backend_user_id = defaultBackendUserId;
                this.logger?.log(`已设置默认负责人ID: ${defaultBackendUserId}`, 'info');
            } else {
                // 使用紧急默认值
                data.incharge_by_backend_user_id = 1;
                this.logger?.log('使用紧急默认负责人ID: 1', 'warning');
            }
        }
        
        // 🔧 修复：确保OTA参考号存在
        if (!data.ota_reference_number) {
            const timestamp = Date.now().toString().slice(-6);
            data.ota_reference_number = `GMH-${timestamp}`;
            this.logger?.log(`已生成OTA参考号: ${data.ota_reference_number}`, 'info');
        }
        
        // 🔧 修复：同步价格处理逻辑（与单订单保持一致）
        this.standardizePriceData(data);
        
        // 🔧 修复：应用智能默认值（与单订单保持一致）
        this.applySmartDefaults(data);
        
        this.logger?.log('🔧 订单数据标准化完成', 'success', { standardizedData: data });
        return data;
    }

    /**
     * 🔧 标准化价格数据（与单订单价格管理器逻辑同步）
     * @param {object} data - 订单数据
     */
    standardizePriceData(data) {
        
        try {
            // 确保价格字段为数字类型
            if (data.ota_price && typeof data.ota_price === 'string') {
                const numericPrice = parseFloat(data.ota_price);
                if (!isNaN(numericPrice)) {
                    data.ota_price = numericPrice;
                    this.logger?.log(`价格转换为数字: ${numericPrice}`, 'info');
                }
            }
            
            // 处理货币转换（如果有货币转换器）
            const currencyConverter = window.OTA?.currencyConverter || window.getCurrencyConverter?.();
            if (currencyConverter && data.ota_price && data.currency) {
                const processedData = currencyConverter.processPriceFromGemini({
                    ota_price: data.ota_price,
                    currency: data.currency
                });
                
                if (processedData) {
                    data.ota_price = processedData.originalPrice || data.ota_price;
                    data.currency = processedData.originalCurrency || data.currency;
                    this.logger?.log('价格货币转换处理完成', 'info', {
                        originalPrice: data.ota_price,
                        currency: data.currency
                    });
                }
            }
            
            // 确保货币字段有默认值
            if (!data.currency) {
                data.currency = 'MYR'; // 默认马来西亚林吉特
                this.logger?.log('已设置默认货币: MYR', 'info');
            }
            
        } catch (error) {
            this.logger?.logError('价格数据标准化失败', error);
        }
    }

    /**
     * 🔧 应用智能默认值（与单订单FormManager逻辑同步）
     * @param {object} data - 订单数据
     */
    applySmartDefaults(data) {
        
        try {
            // 智能设置服务类型（sub_category_id）
            if (!data.sub_category_id) {
                // 根据文本内容智能判断服务类型
                if (data.pickup_location && data.dropoff_location) {
                    data.sub_category_id = 3; // Dropoff
                } else if (data.pickup_location) {
                    data.sub_category_id = 2; // Pickup
                } else {
                    data.sub_category_id = 4; // Charter
                }
                this.logger?.log(`智能设置服务类型: ${data.sub_category_id}`, 'info');
            }
            
            // 智能设置车型（car_type_id）
            if (!data.car_type_id) {
                const passengerCount = parseInt(data.passenger_number) || 1;
                if (passengerCount <= 4) {
                    data.car_type_id = 1; // 轿车
                } else if (passengerCount <= 7) {
                    data.car_type_id = 2; // MPV
                } else {
                    data.car_type_id = 3; // 大型车
                }
                this.logger?.log(`智能设置车型: ${data.car_type_id} (乘客数: ${passengerCount})`, 'info');
            }
            
            // 智能设置驾驶区域
            if (!data.driving_region_id) {
                data.driving_region_id = 1; // 默认区域
                this.logger?.log('已设置默认驾驶区域: 1', 'info');
            }
            
        } catch (error) {
            this.logger?.logError('智能默认值应用失败', error);
        }
    }

    /**
     * 🔧 清理资源（防止内存泄漏）
     */
    cleanup() {
        
        try {
            // 清理防抖定时器
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
                this.debounceTimer = null;
            }
            
            // 清理定期清理机制
            this.stopPeriodicCleanup();
            
            // 清理快捷编辑面板和DOM节点
            this.cleanupQuickEditPanels();
            
            // 清理批量处理状态
            this.cleanupBatchProcessing();
            
            // 清理缓存
            this.clearCache();
            
            // 移除事件监听器
            if (this.boundEventHandlers.has('multiOrderDetected')) {
                document.removeEventListener('multiOrderDetected', this.boundEventHandlers.get('multiOrderDetected'));
            }
            if (this.boundEventHandlers.has('appStateChanged')) {
                document.removeEventListener('appStateChanged', this.boundEventHandlers.get('appStateChanged'));
            }
            if (this.boundEventHandlers.has('beforeunload')) {
                window.removeEventListener('beforeunload', this.boundEventHandlers.get('beforeunload'));
            }
            if (this.boundEventHandlers.has('periodicCleanup')) {
                clearInterval(this.boundEventHandlers.get('periodicCleanup'));
            }
            
            // 移除键盘事件监听
            document.removeEventListener('keydown', this.handleQuickEditEscape);
            
            // 清理状态
            this.state.selectedOrders.clear();
            this.state.processedOrders.clear();
            this.state.currentSegments = [];
            this.state.parsedOrders = [];
            this.boundEventHandlers.clear();
            
            // 清理DOM引用
            this.cleanupDomReferences();
            
            this.logger?.log('🧹 多订单管理器资源已完全清理', 'info');
        } catch (error) {
            this.logger?.logError('多订单管理器清理失败', error);
        }
    }

    /**
     * 🔧 清理缓存
     */
    clearCache() {
        try {
            // 清理结果缓存
            this.state.processedOrders.clear();
            this.state.currentSegments = [];
            this.state.parsedOrders = [];
            
            // 清理选择状态
            this.state.selectedOrders.clear();
            
            getLogger()?.log('🧹 缓存已清理', 'info');
        } catch (error) {
            getLogger()?.logError('清理缓存失败', error);
        }
    }

    /**
     * 🔧 清理快捷编辑面板和DOM节点（防止内存泄漏）
     */
    cleanupQuickEditPanels() {
        try {
            // 清理所有快捷编辑面板
            const overlays = document.querySelectorAll('.quick-edit-overlay');
            overlays.forEach(overlay => {
                // 移除所有事件监听器
                const panel = overlay.querySelector('.quick-edit-panel');
                if (panel) {
                    const inputs = panel.querySelectorAll('input');
                    inputs.forEach(input => {
                        input.removeEventListener('blur', this.handleQuickEditBlur);
                        input.removeEventListener('keypress', this.handleQuickEditKeypress);
                    });
                }
                overlay.remove();
            });
            
            // 清理订单项的编辑状态
            const orderItems = document.querySelectorAll('.order-item.editing');
            orderItems.forEach(item => {
                item.classList.remove('editing');
            });
            
            getLogger()?.log('🧹 快捷编辑面板已清理', 'info');
        } catch (error) {
            getLogger()?.logError('清理快捷编辑面板失败', error);
        }
    }

    /**
     * 🔧 清理批量处理状态
     */
    cleanupBatchProcessing() {
        try {
            // 重置批量进度
            this.state.batchProgress = {
                total: 0,
                completed: 0,
                failed: 0,
                isRunning: false
            };
            
            // 清理批量相关的DOM引用
            const progressBars = document.querySelectorAll('.batch-create-status');
            progressBars.forEach(bar => {
                bar.innerHTML = '';
                bar.style.display = 'none';
            });
            
            getLogger()?.log('🧹 批量处理状态已清理', 'info');
        } catch (error) {
            getLogger()?.logError('清理批量处理状态失败', error);
        }
    }

    /**
     * 🔧 清理DOM引用
     */
    cleanupDomReferences() {
        try {
            // 清理面板拖拽相关的事件监听器
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (multiOrderPanel) {
                const header = multiOrderPanel.querySelector('.multi-order-header');
                if (header) {
                    header.style.cursor = '';
                    header.title = '';
                    delete header.dataset.dragEnabled;
                }
                
                // 重置transform样式
                multiOrderPanel.style.transform = '';
            }
            
            getLogger()?.log('🧹 DOM引用已清理', 'info');
        } catch (error) {
            getLogger()?.logError('清理DOM引用失败', error);
        }
    }

    /**
     * 集成学习系统UI更正功能
     * @param {Array} orders - 订单数组
     */
    integrateLearningSystemUI(orders) {
        try {
            // 检查学习系统是否可用
            const uiCorrectionManager = window.OTA?.uiCorrectionManager || window.uiCorrectionManager;
            if (!uiCorrectionManager) {
                getLogger()?.log('学习系统UI更正管理器不可用，跳过集成', 'info');
                return;
            }

            // 为每个订单项添加学习系统功能
            const orderItems = document.querySelectorAll('.order-item');
            orderItems.forEach((orderItem, index) => {
                if (index < orders.length) {
                    uiCorrectionManager.enhanceOrderItem(orderItem, orders[index], index);
                }
            });

            getLogger()?.log('学习系统UI更正功能集成完成', 'success', {
                enhancedOrders: Math.min(orderItems.length, orders.length)
            });

        } catch (error) {
            getLogger()?.logError('集成学习系统UI更正功能失败', error);
        }
    }

    // 简化为使用固定置信度阈值，提升性能和可维护性

    /**
     * 🚀 简化的时间点和航班特征分析
     * @param {string} text - 输入文本
     * @returns {Object} 分析结果
     */
    analyzeTimePointsAndFlights(text) {
        const analysis = {
            timePoints: [],
            hasFlightFeatures: false,
            isMultiTimePoint: false
        };

        try {
            // 简单时间点提取
            const timeMatches = text.match(/\d{1,2}:\d{2}|\d{4}/g) || [];
            analysis.timePoints = [...new Set(timeMatches)];

            // 简单航班特征检测
            const flightKeywords = ['航班', 'flight', 'AK', 'MH', 'OD'];
            analysis.hasFlightFeatures = flightKeywords.some(keyword =>
                text.toLowerCase().includes(keyword.toLowerCase()));

            // 多时间点判断
            analysis.isMultiTimePoint = analysis.timePoints.length > 1;

            getLogger()?.log('🕐 简化时间点分析完成', 'info', analysis);
            return analysis;

        } catch (error) {
            getLogger()?.logError('时间点分析失败', error);
            return analysis;
        }
    }

    // 简化为固定阈值，无需复杂的学习机制

    /**
     * 获取Gemini配置
     * @returns {Object} Gemini配置对象
     */
    getGeminiConfig() {
        return {
            enabled: true,
            attempts: 2,
            consistencyThreshold: this.state.adaptiveState.currentThreshold || this.config.confidenceThreshold,
            timeout: 25000
        };
    }

    /**
     * **新增**: 获取i18n管理器，带重试机制
     * @returns {Object|null} i18n管理器实例
     */
    getI18nManagerWithRetry() {
        // 尝试多种方式获取i18n管理器
        const attempts = [
            () => window.getI18nManager && window.getI18nManager(),
            () => window.OTA && window.OTA.i18nManager,
            () => window.i18nManager
        ];

        for (const attempt of attempts) {
            try {
                const manager = attempt();
                if (manager && typeof manager.t === 'function') {
                    return manager;
                }
            } catch (error) {
                // 继续尝试下一种方式
            }
        }

        return null;
    }

    // ...existing code...
}

// 全局实例变量
let multiOrderManagerInstance = null;

/**
 * @OTA_FACTORY 创建多订单管理器实例
 * 🏷️ 标签: @OTA_MULTI_ORDER_MANAGER_FACTORY
 * 📝 说明: 单例工厂函数，获取多订单管理器实例
 * ⚠️ 警告: 已注册，请勿重复开发
 * @returns {MultiOrderManager} 管理器实例
 */
function getMultiOrderManager() {
    if (!multiOrderManagerInstance) {
        multiOrderManagerInstance = new MultiOrderManager();
    }
    return multiOrderManagerInstance;
}

// 导出到全局作用域
window.MultiOrderManager = MultiOrderManager;
window.getMultiOrderManager = getMultiOrderManager;

// 确保OTA命名空间存在并暴露管理器
window.OTA = window.OTA || {};
window.OTA.MultiOrderManager = MultiOrderManager;
window.OTA.getMultiOrderManager = getMultiOrderManager;
window.OTA.multiOrderManager = getMultiOrderManager();

// 向后兼容
window.multiOrderManager = getMultiOrderManager();

// 注册到OTA注册中心
if (window.OTA && window.OTA.Registry) {
    const instance = getMultiOrderManager();
    window.OTA.Registry.registerManager('multiOrderManager', instance, '@OTA_MULTI_ORDER_MANAGER');
    window.OTA.Registry.registerFactory('getMultiOrderManager', getMultiOrderManager, '@OTA_MULTI_ORDER_MANAGER_FACTORY');
}

// 结束防重复加载检查
}
